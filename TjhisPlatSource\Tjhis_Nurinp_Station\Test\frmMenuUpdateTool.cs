using System;
using System.Drawing;
using System.Windows.Forms;
using Tjhis.Nurinp.Station.Service;

namespace Tjhis.Nurinp.Station.Test
{
    /// <summary>
    /// 菜单更新工具窗体
    /// 用于手动执行菜单更新操作
    /// </summary>
    public partial class frmMenuUpdateTool : Form
    {
        private Button btnUpdateMenu;
        private Button btnValidateMenu;
        private Button btnGenerateReport;
        private TextBox txtLog;
        private Label lblTitle;
        private Label lblDescription;
        
        public frmMenuUpdateTool()
        {
            InitializeComponent();
        }
        
        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // 窗体设置
            this.Text = "NURINP护理记录单菜单更新工具";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            
            // 标题标签
            this.lblTitle = new Label();
            this.lblTitle.Text = "住院护理站菜单更新工具";
            this.lblTitle.Font = new Font("微软雅黑", 14F, FontStyle.Bold);
            this.lblTitle.Location = new Point(20, 20);
            this.lblTitle.Size = new Size(300, 30);
            this.Controls.Add(this.lblTitle);
            
            // 描述标签
            this.lblDescription = new Label();
            this.lblDescription.Text = "此工具用于将护理记录单功能添加到日常工作菜单中。\n点击下方按钮执行相应操作：";
            this.lblDescription.Location = new Point(20, 60);
            this.lblDescription.Size = new Size(550, 40);
            this.Controls.Add(this.lblDescription);
            
            // 更新菜单按钮
            this.btnUpdateMenu = new Button();
            this.btnUpdateMenu.Text = "执行菜单更新";
            this.btnUpdateMenu.Location = new Point(20, 120);
            this.btnUpdateMenu.Size = new Size(120, 35);
            this.btnUpdateMenu.BackColor = Color.LightGreen;
            this.btnUpdateMenu.Click += BtnUpdateMenu_Click;
            this.Controls.Add(this.btnUpdateMenu);
            
            // 验证菜单按钮
            this.btnValidateMenu = new Button();
            this.btnValidateMenu.Text = "验证菜单配置";
            this.btnValidateMenu.Location = new Point(160, 120);
            this.btnValidateMenu.Size = new Size(120, 35);
            this.btnValidateMenu.BackColor = Color.LightBlue;
            this.btnValidateMenu.Click += BtnValidateMenu_Click;
            this.Controls.Add(this.btnValidateMenu);
            
            // 生成报告按钮
            this.btnGenerateReport = new Button();
            this.btnGenerateReport.Text = "生成配置报告";
            this.btnGenerateReport.Location = new Point(300, 120);
            this.btnGenerateReport.Size = new Size(120, 35);
            this.btnGenerateReport.BackColor = Color.LightYellow;
            this.btnGenerateReport.Click += BtnGenerateReport_Click;
            this.Controls.Add(this.btnGenerateReport);
            
            // 日志文本框
            this.txtLog = new TextBox();
            this.txtLog.Location = new Point(20, 180);
            this.txtLog.Size = new Size(550, 250);
            this.txtLog.Multiline = true;
            this.txtLog.ScrollBars = ScrollBars.Vertical;
            this.txtLog.ReadOnly = true;
            this.txtLog.Font = new Font("Consolas", 9F);
            this.txtLog.BackColor = Color.Black;
            this.txtLog.ForeColor = Color.LightGreen;
            this.Controls.Add(this.txtLog);
            
            this.ResumeLayout(false);
        }
        
        private void BtnUpdateMenu_Click(object sender, EventArgs e)
        {
            try
            {
                LogMessage("开始执行菜单更新...");
                
                bool result = ExecuteMenuUpdate.UpdateNursingRecordMenu(false);
                
                if (result)
                {
                    LogMessage("✓ 菜单更新成功！");
                    LogMessage("请重新启动应用程序以查看更新后的菜单。");
                    
                    MessageBox.Show(
                        "菜单更新成功！\n\n请重新启动住院护理工作站应用程序，\n然后在顶部菜单栏的'日常工作'菜单中查看'护理记录单'选项。",
                        "更新成功",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information);
                }
                else
                {
                    LogMessage("✗ 菜单更新失败！");
                    LogMessage("请查看日志文件了解详细错误信息。");
                    
                    MessageBox.Show(
                        "菜单更新失败！\n\n请查看日志文件了解详细错误信息。",
                        "更新失败",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                LogMessage($"✗ 更新过程中发生异常: {ex.Message}");
                MessageBox.Show($"更新过程中发生异常：\n\n{ex.Message}", "异常", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void BtnValidateMenu_Click(object sender, EventArgs e)
        {
            try
            {
                LogMessage("开始验证菜单配置...");
                
                MenuUpdateHelper helper = new MenuUpdateHelper();
                MenuUpdateResult result = helper.ValidateMenuUpdate();
                
                LogMessage($"验证结果: {(result.IsSuccess ? "通过" : "失败")}");
                LogMessage($"验证消息: {result.Message}");
                LogMessage($"主菜单中存在: {(result.ExistsInMainMenu ? "是" : "否")}");
                LogMessage($"右键菜单中存在: {(result.ExistsInRightMenu ? "是" : "否")}");
                
                MessageBox.Show(
                    $"验证结果: {(result.IsSuccess ? "通过" : "失败")}\n\n{result.Message}",
                    "验证结果",
                    MessageBoxButtons.OK,
                    result.IsSuccess ? MessageBoxIcon.Information : MessageBoxIcon.Warning);
            }
            catch (Exception ex)
            {
                LogMessage($"✗ 验证过程中发生异常: {ex.Message}");
                MessageBox.Show($"验证过程中发生异常：\n\n{ex.Message}", "异常", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void BtnGenerateReport_Click(object sender, EventArgs e)
        {
            try
            {
                LogMessage("开始生成配置报告...");
                
                MenuUpdateHelper helper = new MenuUpdateHelper();
                string report = helper.GenerateMenuConfigReport();
                
                // 显示报告
                Form reportForm = new Form();
                reportForm.Text = "菜单配置报告";
                reportForm.Size = new Size(700, 600);
                reportForm.StartPosition = FormStartPosition.CenterParent;
                
                TextBox txtReport = new TextBox();
                txtReport.Dock = DockStyle.Fill;
                txtReport.Multiline = true;
                txtReport.ScrollBars = ScrollBars.Both;
                txtReport.ReadOnly = true;
                txtReport.Font = new Font("Consolas", 9F);
                txtReport.Text = report;
                
                reportForm.Controls.Add(txtReport);
                reportForm.ShowDialog(this);
                
                LogMessage("✓ 配置报告生成完成");
            }
            catch (Exception ex)
            {
                LogMessage($"✗ 生成报告过程中发生异常: {ex.Message}");
                MessageBox.Show($"生成报告过程中发生异常：\n\n{ex.Message}", "异常", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void LogMessage(string message)
        {
            string timestamp = DateTime.Now.ToString("HH:mm:ss");
            txtLog.AppendText($"[{timestamp}] {message}\r\n");
            txtLog.SelectionStart = txtLog.Text.Length;
            txtLog.ScrollToCaret();
            Application.DoEvents();
        }
        
        private void frmMenuUpdateTool_Load(object sender, EventArgs e)
        {
            LogMessage("菜单更新工具已启动");
            LogMessage("准备为NURINP应用更新菜单配置");
            
            // 检查是否需要更新
            if (ExecuteMenuUpdate.NeedsMenuUpdate())
            {
                LogMessage("检测到菜单需要更新");
            }
            else
            {
                LogMessage("菜单配置已是最新状态");
            }
        }
    }
}
