using System;
using System.Data;
using System.Collections;
using PlatCommon.Common;

namespace Tjhis.Nurob.Station.Service
{
    /// <summary>
    /// 菜单更新管理器
    /// 用于动态添加和更新菜单项，不依赖系统初始化
    /// </summary>
    public class MenuUpdateManager
    {
        private NM_Service.NMService.ServerPublicClient ws = new NM_Service.NMService.ServerPublicClient();
        
        /// <summary>
        /// 添加护理记录单到日常工作菜单
        /// </summary>
        /// <returns>操作结果</returns>
        public bool AddNursingRecordToMainMenu()
        {
            try
            {
                MenuChangeLog.WriteLog("INFO", "菜单更新", "开始添加护理记录单到日常工作菜单");
                
                // 检查菜单项是否已存在
                string checkSql = "SELECT COUNT(*) FROM COMM.SEC_MENUS_DICT WHERE APPLICATION_CODE = 'NUROB' AND MENU_NAME = 'NUROB_MAIN0570'";
                DataSet checkResult = ws.GetDataBySql(checkSql, "CHECK_RESULT", false);
                
                if (checkResult.Tables[0].Rows.Count > 0 && 
                    Convert.ToInt32(checkResult.Tables[0].Rows[0][0]) > 0)
                {
                    MenuChangeLog.WriteLog("WARN", "菜单更新", "护理记录单菜单项已存在，跳过添加");
                    return true;
                }
                
                // 插入新的菜单项
                string insertSql = @"INSERT INTO COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
VALUES ('NUROB', 'NUROB_MAIN0570', 'MainWindow', '', 570, '护理记录单', '', 'NUROB_MAIN05', 'TJHIS.NUROB.Severe.frmSevereRecord', null, '1', '1', 'Images/Menu/Big/00-护理病历.png', 'Images/Menu/Small/00-护理病历.png', '留观护理主菜单', '', 'TJHIS_NUROB_Severe.dll')";
                
                ArrayList sqlList = new ArrayList();
                sqlList.Add(insertSql);
                
                // 执行SQL
                ws.SaveTablesData(sqlList);
                
                MenuChangeLog.WriteLog("INFO", "菜单更新", "成功添加护理记录单到日常工作菜单");
                MenuChangeLog.LogNursingRecordMenuMove();
                
                return true;
            }
            catch (Exception ex)
            {
                MenuChangeLog.WriteLog("ERROR", "菜单更新", "添加护理记录单菜单失败", ex);
                return false;
            }
        }
        
        /// <summary>
        /// 移除右键菜单中的护理记录单（可选操作）
        /// </summary>
        /// <returns>操作结果</returns>
        public bool RemoveNursingRecordFromRightClickMenu()
        {
            try
            {
                MenuChangeLog.WriteLog("INFO", "菜单更新", "开始从右键菜单移除护理记录单");
                
                string deleteSql = "DELETE FROM COMM.SEC_MENUS_DICT WHERE APPLICATION_CODE = 'NUROB' AND MENU_NAME = 'NUROB_CONTEXT170'";
                
                ArrayList sqlList = new ArrayList();
                sqlList.Add(deleteSql);
                
                // 执行SQL
                ws.SaveTablesData(sqlList);
                
                MenuChangeLog.WriteLog("INFO", "菜单更新", "成功从右键菜单移除护理记录单");
                
                return true;
            }
            catch (Exception ex)
            {
                MenuChangeLog.WriteLog("ERROR", "菜单更新", "从右键菜单移除护理记录单失败", ex);
                return false;
            }
        }
        
        /// <summary>
        /// 验证菜单更新结果
        /// </summary>
        /// <returns>验证结果</returns>
        public MenuUpdateResult ValidateMenuUpdate()
        {
            MenuUpdateResult result = new MenuUpdateResult();
            
            try
            {
                // 检查日常工作菜单中是否存在护理记录单
                string checkMainMenuSql = @"SELECT COUNT(*) FROM COMM.SEC_MENUS_DICT 
                                           WHERE APPLICATION_CODE = 'NUROB' 
                                           AND MENU_NAME = 'NUROB_MAIN0570' 
                                           AND SUPPER_MENU = 'NUROB_MAIN05'";
                
                DataSet mainMenuResult = ws.GetDataBySql(checkMainMenuSql, "MAIN_MENU_CHECK", false);
                bool existsInMainMenu = Convert.ToInt32(mainMenuResult.Tables[0].Rows[0][0]) > 0;
                
                // 检查右键菜单中是否仍存在护理记录单
                string checkRightMenuSql = @"SELECT COUNT(*) FROM COMM.SEC_MENUS_DICT 
                                            WHERE APPLICATION_CODE = 'NUROB' 
                                            AND MENU_NAME = 'NUROB_CONTEXT170'";
                
                DataSet rightMenuResult = ws.GetDataBySql(checkRightMenuSql, "RIGHT_MENU_CHECK", false);
                bool existsInRightMenu = Convert.ToInt32(rightMenuResult.Tables[0].Rows[0][0]) > 0;
                
                result.ExistsInMainMenu = existsInMainMenu;
                result.ExistsInRightMenu = existsInRightMenu;
                result.IsSuccess = existsInMainMenu;
                
                if (existsInMainMenu)
                {
                    result.Message = "护理记录单已成功添加到日常工作菜单";
                    if (existsInRightMenu)
                    {
                        result.Message += "，右键菜单中仍保留该功能";
                    }
                }
                else
                {
                    result.Message = "护理记录单未在日常工作菜单中找到";
                }
                
                MenuChangeLog.WriteLog("INFO", "菜单验证", result.Message);
                
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.Message = $"验证菜单更新时发生异常：{ex.Message}";
                MenuChangeLog.WriteLog("ERROR", "菜单验证", "验证菜单更新异常", ex);
            }
            
            return result;
        }
        
        /// <summary>
        /// 刷新菜单缓存（如果系统有缓存机制）
        /// </summary>
        public void RefreshMenuCache()
        {
            try
            {
                MenuChangeLog.WriteLog("INFO", "菜单刷新", "开始刷新菜单缓存");
                
                // 这里可以添加清除菜单缓存的逻辑
                // 具体实现取决于系统的缓存机制
                
                MenuChangeLog.WriteLog("INFO", "菜单刷新", "菜单缓存刷新完成");
            }
            catch (Exception ex)
            {
                MenuChangeLog.WriteLog("ERROR", "菜单刷新", "刷新菜单缓存异常", ex);
            }
        }
    }
    
    /// <summary>
    /// 菜单更新结果
    /// </summary>
    public class MenuUpdateResult
    {
        public bool IsSuccess { get; set; } = false;
        public string Message { get; set; } = string.Empty;
        public bool ExistsInMainMenu { get; set; } = false;
        public bool ExistsInRightMenu { get; set; } = false;
    }
}
