using System;
using System.IO;
using System.Windows.Forms;

namespace Tjhis.Nurinp.Station.Service
{
    /// <summary>
    /// 执行菜单更新的工具类
    /// 可以在应用程序启动时调用，或者作为独立工具运行
    /// </summary>
    public static class ExecuteMenuUpdate
    {
        /// <summary>
        /// 执行护理记录单菜单更新
        /// </summary>
        /// <param name="showMessageBox">是否显示消息框</param>
        /// <returns>更新是否成功</returns>
        public static bool UpdateNursingRecordMenu(bool showMessageBox = true)
        {
            try
            {
                MenuUpdateHelper helper = new MenuUpdateHelper();
                
                // 执行菜单更新
                bool updateResult = helper.AddNursingRecordToMainMenu();
                
                if (updateResult)
                {
                    // 验证更新结果
                    MenuUpdateResult validation = helper.ValidateMenuUpdate();
                    
                    // 生成报告
                    string report = helper.GenerateMenuConfigReport();
                    
                    // 保存报告到文件
                    string reportPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, 
                        "..\\Client\\LOG\\exLOG\\", $"NurinpMenuReport_{DateTime.Now:yyyyMMdd_HHmmss}.txt");
                    
                    Directory.CreateDirectory(Path.GetDirectoryName(reportPath));
                    File.WriteAllText(reportPath, report, System.Text.Encoding.UTF8);
                    
                    if (showMessageBox)
                    {
                        if (validation.IsSuccess)
                        {
                            MessageBox.Show(
                                "护理记录单菜单更新成功！\n\n" +
                                "请重新启动应用程序，然后在顶部菜单栏的'日常工作'菜单中查看'护理记录单'选项。\n\n" +
                                $"详细报告已保存到：{reportPath}",
                                "菜单更新成功",
                                MessageBoxButtons.OK,
                                MessageBoxIcon.Information);
                        }
                        else
                        {
                            MessageBox.Show(
                                "菜单更新完成，但验证时发现问题：\n\n" +
                                validation.Message + "\n\n" +
                                $"请查看详细报告：{reportPath}",
                                "菜单更新警告",
                                MessageBoxButtons.OK,
                                MessageBoxIcon.Warning);
                        }
                    }
                    
                    return validation.IsSuccess;
                }
                else
                {
                    if (showMessageBox)
                    {
                        MessageBox.Show(
                            "菜单更新失败！\n\n请查看日志文件了解详细错误信息。",
                            "菜单更新失败",
                            MessageBoxButtons.OK,
                            MessageBoxIcon.Error);
                    }
                    
                    return false;
                }
            }
            catch (Exception ex)
            {
                if (showMessageBox)
                {
                    MessageBox.Show(
                        $"菜单更新过程中发生异常：\n\n{ex.Message}\n\n{ex.StackTrace}",
                        "菜单更新异常",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Error);
                }
                
                return false;
            }
        }
        
        /// <summary>
        /// 静默执行菜单更新（不显示消息框）
        /// </summary>
        /// <returns>更新是否成功</returns>
        public static bool SilentUpdateNursingRecordMenu()
        {
            return UpdateNursingRecordMenu(false);
        }
        
        /// <summary>
        /// 检查菜单是否需要更新
        /// </summary>
        /// <returns>是否需要更新</returns>
        public static bool NeedsMenuUpdate()
        {
            try
            {
                MenuUpdateHelper helper = new MenuUpdateHelper();
                MenuUpdateResult result = helper.ValidateMenuUpdate();
                return !result.ExistsInMainMenu;
            }
            catch
            {
                return true; // 如果检查失败，假设需要更新
            }
        }
    }
}
