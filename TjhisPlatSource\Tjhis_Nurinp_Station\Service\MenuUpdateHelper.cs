using System;
using System.Collections;
using System.Data;
using System.IO;
using System.Text;
using PlatCommon.Common;

namespace Tjhis.Nurinp.Station.Service
{
    /// <summary>
    /// 菜单更新助手
    /// 用于动态添加和更新NURINP应用的菜单项
    /// </summary>
    public class MenuUpdateHelper
    {
        private NM_Service.NMService.ServerPublicClient ws = new NM_Service.NMService.ServerPublicClient();
        private static readonly string LogPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..\\Client\\LOG\\exLOG\\");
        
        /// <summary>
        /// 添加护理记录单到日常工作菜单
        /// </summary>
        /// <returns>操作结果</returns>
        public bool AddNursingRecordToMainMenu()
        {
            try
            {
                WriteLog("INFO", "菜单更新", "开始为NURINP应用添加护理记录单到日常工作菜单");
                
                // 检查菜单项是否已存在
                string checkSql = "SELECT COUNT(*) FROM COMM.SEC_MENUS_DICT WHERE APPLICATION_CODE = 'NURINP' AND MENU_NAME = 'NURINP_MAIN0570'";
                DataSet checkResult = ws.GetDataBySql(checkSql, "CHECK_RESULT", false);
                
                if (checkResult.Tables[0].Rows.Count > 0 && 
                    Convert.ToInt32(checkResult.Tables[0].Rows[0][0]) > 0)
                {
                    WriteLog("WARN", "菜单更新", "护理记录单菜单项已存在，跳过添加");
                    return true;
                }
                
                // 首先检查是否存在日常工作主菜单
                string checkMainMenuSql = "SELECT COUNT(*) FROM COMM.SEC_MENUS_DICT WHERE APPLICATION_CODE = 'NURINP' AND MENU_NAME = 'NURINP_MAIN05'";
                DataSet mainMenuResult = ws.GetDataBySql(checkMainMenuSql, "MAIN_MENU_CHECK", false);
                
                ArrayList sqlList = new ArrayList();
                
                // 如果日常工作主菜单不存在，先创建它
                if (mainMenuResult.Tables[0].Rows.Count == 0 || 
                    Convert.ToInt32(mainMenuResult.Tables[0].Rows[0][0]) == 0)
                {
                    WriteLog("INFO", "菜单更新", "日常工作主菜单不存在，先创建主菜单");
                    
                    string createMainMenuSql = @"INSERT INTO COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
VALUES ('NURINP', 'NURINP_MAIN05', 'MainWindow', '', 500, '日常工作', '', 'parent', '', null, '1', '2', '', '', '住院护理主菜单', '', '')";
                    
                    sqlList.Add(createMainMenuSql);
                }
                
                // 插入护理记录单菜单项
                string insertSql = @"INSERT INTO COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
VALUES ('NURINP', 'NURINP_MAIN0570', 'MainWindow', '', 570, '护理记录单', '', 'NURINP_MAIN05', 'TjhisAppPatientView.NursingRecordSheet.frmNursingRecord', null, '1', '1', 'Images/Menu/Big/00-护理病历.png', 'Images/Menu/Small/00-护理病历.png', '住院护理主菜单', '', 'TjhisAppPatientView.dll')";
                
                sqlList.Add(insertSql);
                
                // 执行SQL
                ws.SaveTablesData(sqlList);
                
                WriteLog("INFO", "菜单更新", "成功添加护理记录单到日常工作菜单");
                WriteLog("INFO", "护理记录单", "已将护理记录单功能添加到日常工作菜单中");
                WriteLog("DEBUG", "护理记录单", "新菜单项配置 - MENU_NAME: NURINP_MAIN0570, SERIAL_NO: 570, PARENT: NURINP_MAIN05");
                
                return true;
            }
            catch (Exception ex)
            {
                WriteLog("ERROR", "菜单更新", "添加护理记录单菜单失败", ex);
                return false;
            }
        }
        
        /// <summary>
        /// 验证菜单更新结果
        /// </summary>
        /// <returns>验证结果</returns>
        public MenuUpdateResult ValidateMenuUpdate()
        {
            MenuUpdateResult result = new MenuUpdateResult();
            
            try
            {
                WriteLog("INFO", "菜单验证", "开始验证NURINP应用的菜单更新结果");
                
                // 检查日常工作菜单中是否存在护理记录单
                string checkMainMenuSql = @"SELECT COUNT(*) FROM COMM.SEC_MENUS_DICT 
                                           WHERE APPLICATION_CODE = 'NURINP' 
                                           AND MENU_NAME = 'NURINP_MAIN0570' 
                                           AND SUPPER_MENU = 'NURINP_MAIN05'";
                
                DataSet mainMenuResult = ws.GetDataBySql(checkMainMenuSql, "MAIN_MENU_CHECK", false);
                bool existsInMainMenu = Convert.ToInt32(mainMenuResult.Tables[0].Rows[0][0]) > 0;
                
                // 检查右键菜单中是否存在护理记录单
                string checkRightMenuSql = @"SELECT COUNT(*) FROM COMM.SEC_MENUS_DICT 
                                            WHERE APPLICATION_CODE = 'NURINP' 
                                            AND MENU_GROUP = '床头卡右键菜单'
                                            AND MENU_TEXT = '护理记录单'";
                
                DataSet rightMenuResult = ws.GetDataBySql(checkRightMenuSql, "RIGHT_MENU_CHECK", false);
                bool existsInRightMenu = Convert.ToInt32(rightMenuResult.Tables[0].Rows[0][0]) > 0;
                
                result.ExistsInMainMenu = existsInMainMenu;
                result.ExistsInRightMenu = existsInRightMenu;
                result.IsSuccess = existsInMainMenu;
                
                if (existsInMainMenu)
                {
                    result.Message = "护理记录单已成功添加到日常工作菜单";
                    if (existsInRightMenu)
                    {
                        result.Message += "，右键菜单中也存在该功能";
                    }
                    else
                    {
                        result.Message += "，右键菜单中暂无该功能";
                    }
                }
                else
                {
                    result.Message = "护理记录单未在日常工作菜单中找到";
                }
                
                WriteLog("INFO", "菜单验证", result.Message);
                
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.Message = $"验证菜单更新时发生异常：{ex.Message}";
                WriteLog("ERROR", "菜单验证", "验证菜单更新异常", ex);
            }
            
            return result;
        }
        
        /// <summary>
        /// 生成菜单配置报告
        /// </summary>
        /// <returns>配置报告</returns>
        public string GenerateMenuConfigReport()
        {
            StringBuilder report = new StringBuilder();
            
            report.AppendLine("=== NURINP住院护理站菜单配置变更报告 ===");
            report.AppendLine($"生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine();
            
            report.AppendLine("变更内容:");
            report.AppendLine("1. 在日常工作菜单中新增护理记录单功能");
            report.AppendLine("   - 应用代码: NURINP");
            report.AppendLine("   - 菜单名称: NURINP_MAIN0570");
            report.AppendLine("   - 显示文本: 护理记录单");
            report.AppendLine("   - 序号: 570");
            report.AppendLine("   - 父菜单: NURINP_MAIN05 (日常工作)");
            report.AppendLine("   - 打开窗体: TjhisAppPatientView.NursingRecordSheet.frmNursingRecord");
            report.AppendLine("   - 程序集: TjhisAppPatientView.dll");
            report.AppendLine();
            
            report.AppendLine("使用说明:");
            report.AppendLine("1. 重新启动住院护理工作站应用程序");
            report.AppendLine("2. 在顶部菜单栏中找到'日常工作'菜单");
            report.AppendLine("3. 点击'日常工作'菜单，应该能看到'护理记录单'选项");
            report.AppendLine("4. 点击'护理记录单'选项，应该能正常打开护理记录单窗体");
            report.AppendLine();
            
            // 执行验证
            MenuUpdateResult validation = ValidateMenuUpdate();
            report.AppendLine("配置验证结果:");
            report.AppendLine($"验证状态: {(validation.IsSuccess ? "通过" : "失败")}");
            report.AppendLine($"验证消息: {validation.Message}");
            report.AppendLine($"主菜单中存在: {(validation.ExistsInMainMenu ? "是" : "否")}");
            report.AppendLine($"右键菜单中存在: {(validation.ExistsInRightMenu ? "是" : "否")}");
            
            report.AppendLine();
            report.AppendLine("=== 报告结束 ===");
            
            return report.ToString();
        }
        
        /// <summary>
        /// 写入日志
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <param name="module">模块名称</param>
        /// <param name="message">消息</param>
        /// <param name="exception">异常</param>
        private void WriteLog(string level, string module, string message, Exception exception = null)
        {
            try
            {
                if (!Directory.Exists(LogPath))
                {
                    Directory.CreateDirectory(LogPath);
                }

                string logFileName = $"NurinpMenuChange_{DateTime.Now:yyyyMMdd}.log";
                string fullLogPath = Path.Combine(LogPath, logFileName);
                string timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                
                StringBuilder logEntry = new StringBuilder();
                logEntry.AppendLine($"[{timestamp}] [{level}] [{module}] {message}");
                
                if (exception != null)
                {
                    logEntry.AppendLine($"异常详情: {exception.Message}");
                    logEntry.AppendLine($"堆栈跟踪: {exception.StackTrace}");
                }
                
                File.AppendAllText(fullLogPath, logEntry.ToString(), Encoding.UTF8);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"日志记录失败: {ex.Message}");
            }
        }
    }
    
    /// <summary>
    /// 菜单更新结果
    /// </summary>
    public class MenuUpdateResult
    {
        public bool IsSuccess { get; set; } = false;
        public string Message { get; set; } = string.Empty;
        public bool ExistsInMainMenu { get; set; } = false;
        public bool ExistsInRightMenu { get; set; } = false;
    }
}
