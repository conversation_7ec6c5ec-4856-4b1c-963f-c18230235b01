using System;
using System.Windows.Forms;

namespace Tjhis.Nurinp.Station.Test
{
    /// <summary>
    /// 菜单更新工具启动程序
    /// </summary>
    static class Program
    {
        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            
            try
            {
                // 启动菜单更新工具
                Application.Run(new frmMenuUpdateTool());
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"启动菜单更新工具时发生异常：\n\n{ex.Message}\n\n{ex.StackTrace}",
                    "启动异常",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }
    }
}
