using System;
using System.IO;
using System.Text;

namespace Tjhis_Nurob_Station.Service
{
    /// <summary>
    /// 菜单变更日志记录类
    /// 用于记录菜单配置的变更操作，便于调试和追踪
    /// </summary>
    public static class MenuChangeLog
    {
        private static readonly string LogPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..\\Client\\LOG\\exLOG\\");
        private static readonly string LogFileName = $"MenuChange_{DateTime.Now:yyyyMMdd}.log";
        
        /// <summary>
        /// 记录菜单变更日志
        /// </summary>
        /// <param name="level">日志级别 (INFO/WARN/ERROR/DEBUG)</param>
        /// <param name="module">模块名称</param>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常信息（可选）</param>
        public static void WriteLog(string level, string module, string message, Exception exception = null)
        {
            try
            {
                // 确保日志目录存在
                if (!Directory.Exists(LogPath))
                {
                    Directory.CreateDirectory(LogPath);
                }

                string fullLogPath = Path.Combine(LogPath, LogFileName);
                string timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                
                StringBuilder logEntry = new StringBuilder();
                logEntry.AppendLine($"[{timestamp}] [{level}] [{module}] {message}");
                
                if (exception != null)
                {
                    logEntry.AppendLine($"异常详情: {exception.Message}");
                    logEntry.AppendLine($"堆栈跟踪: {exception.StackTrace}");
                }
                
                // 写入日志文件
                File.AppendAllText(fullLogPath, logEntry.ToString(), Encoding.UTF8);
                
                // 清理超过30天的旧日志文件
                CleanOldLogFiles();
            }
            catch (Exception ex)
            {
                // 日志记录失败时，避免影响主程序运行
                System.Diagnostics.Debug.WriteLine($"日志记录失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 记录菜单初始化信息
        /// </summary>
        /// <param name="menuName">菜单名称</param>
        /// <param name="menuText">菜单显示文本</param>
        /// <param name="serialNo">序号</param>
        /// <param name="parentMenu">父菜单</param>
        public static void LogMenuInitialization(string menuName, string menuText, int serialNo, string parentMenu)
        {
            string message = $"初始化菜单项 - 名称: {menuName}, 显示文本: {menuText}, 序号: {serialNo}, 父菜单: {parentMenu}";
            WriteLog("INFO", "菜单初始化", message);
        }
        
        /// <summary>
        /// 记录菜单变更操作
        /// </summary>
        /// <param name="operation">操作类型 (ADD/MOVE/DELETE/MODIFY)</param>
        /// <param name="menuName">菜单名称</param>
        /// <param name="fromLocation">原位置</param>
        /// <param name="toLocation">目标位置</param>
        public static void LogMenuChange(string operation, string menuName, string fromLocation, string toLocation)
        {
            string message = $"菜单变更操作 - 操作: {operation}, 菜单: {menuName}, 从: {fromLocation}, 到: {toLocation}";
            WriteLog("INFO", "菜单变更", message);
        }
        
        /// <summary>
        /// 清理超过30天的旧日志文件
        /// </summary>
        private static void CleanOldLogFiles()
        {
            try
            {
                if (!Directory.Exists(LogPath)) return;
                
                string[] logFiles = Directory.GetFiles(LogPath, "MenuChange_*.log");
                DateTime cutoffDate = DateTime.Now.AddDays(-30);
                
                foreach (string logFile in logFiles)
                {
                    FileInfo fileInfo = new FileInfo(logFile);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        File.Delete(logFile);
                        WriteLog("DEBUG", "日志清理", $"删除过期日志文件: {Path.GetFileName(logFile)}");
                    }
                }
            }
            catch (Exception ex)
            {
                WriteLog("WARN", "日志清理", "清理旧日志文件时发生异常", ex);
            }
        }
        
        /// <summary>
        /// 记录护理记录单菜单移动操作
        /// </summary>
        public static void LogNursingRecordMenuMove()
        {
            LogMenuChange("MOVE", "护理记录单", "床头卡右键菜单", "日常工作菜单");
            WriteLog("INFO", "护理记录单", "已将护理记录单功能从右键菜单移动到日常工作菜单中");
            WriteLog("DEBUG", "护理记录单", "新菜单项配置 - MENU_NAME: NUROB_MAIN0570, SERIAL_NO: 570, PARENT: NUROB_MAIN05");
        }
    }
}
