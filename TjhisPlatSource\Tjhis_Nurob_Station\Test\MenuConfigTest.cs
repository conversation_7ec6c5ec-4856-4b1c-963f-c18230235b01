using System;
using System.IO;
using Tjhis_Nurob_Station.Service;

namespace Tjhis_Nurob_Station.Test
{
    /// <summary>
    /// 菜单配置测试类
    /// 用于测试和验证菜单配置的正确性
    /// </summary>
    public class MenuConfigTest
    {
        /// <summary>
        /// 执行菜单配置测试
        /// </summary>
        public static void RunMenuConfigTest()
        {
            Console.WriteLine("=== 护理记录单菜单配置测试 ===");
            Console.WriteLine($"测试开始时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            Console.WriteLine();
            
            try
            {
                // 1. 生成配置报告
                Console.WriteLine("1. 生成菜单配置报告...");
                string report = MenuConfigValidator.GenerateMenuConfigReport();
                
                // 保存报告到文件
                string reportPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, 
                    "..\\Client\\LOG\\exLOG\\", $"MenuConfigReport_{DateTime.Now:yyyyMMdd_HHmmss}.txt");
                
                Directory.CreateDirectory(Path.GetDirectoryName(reportPath));
                File.WriteAllText(reportPath, report, System.Text.Encoding.UTF8);
                
                Console.WriteLine($"配置报告已保存到: {reportPath}");
                Console.WriteLine();
                
                // 2. 显示报告内容
                Console.WriteLine("2. 配置报告内容:");
                Console.WriteLine(report);
                
                // 3. 执行验证测试
                Console.WriteLine("3. 执行菜单配置验证...");
                ValidationResult result = MenuConfigValidator.ValidateNursingRecordMenuConfig();
                
                Console.WriteLine($"验证结果: {(result.IsValid ? "通过" : "失败")}");
                Console.WriteLine($"验证消息: {result.Message}");
                
                if (result.Details.Count > 0)
                {
                    Console.WriteLine("验证详情:");
                    foreach (string detail in result.Details)
                    {
                        Console.WriteLine($"  {detail}");
                    }
                }
                
                Console.WriteLine();
                
                // 4. 提供使用指导
                Console.WriteLine("4. 使用指导:");
                Console.WriteLine("   a) 重新启动护理工作站应用程序");
                Console.WriteLine("   b) 在顶部菜单栏中找到'日常工作'菜单");
                Console.WriteLine("   c) 点击'日常工作'菜单，应该能看到'护理记录单'选项");
                Console.WriteLine("   d) 点击'护理记录单'选项，应该能正常打开护理记录单窗体");
                Console.WriteLine();
                
                // 5. 注意事项
                Console.WriteLine("5. 注意事项:");
                Console.WriteLine("   - 右键菜单中的护理记录单功能仍然保留");
                Console.WriteLine("   - 如需完全移除右键菜单中的护理记录单，需要额外配置");
                Console.WriteLine("   - 建议在测试环境中先验证功能正常后再部署到生产环境");
                Console.WriteLine();
                
                Console.WriteLine("=== 测试完成 ===");
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生异常: {ex.Message}");
                Console.WriteLine($"异常详情: {ex.StackTrace}");
                
                // 记录异常到日志
                MenuChangeLog.WriteLog("ERROR", "菜单测试", "菜单配置测试异常", ex);
            }
        }
        
        /// <summary>
        /// 显示菜单结构信息
        /// </summary>
        public static void ShowMenuStructure()
        {
            Console.WriteLine("=== 菜单结构信息 ===");
            Console.WriteLine();
            
            Console.WriteLine("主菜单结构:");
            Console.WriteLine("├── 患者入出转 (NUROB_MAIN01)");
            Console.WriteLine("├── 物资处理 (NUROB_MAIN02)");
            Console.WriteLine("├── 查询统计 (NUROB_MAIN03)");
            Console.WriteLine("├── 费用查询 (NUROB_MAIN04)");
            Console.WriteLine("├── 日常工作 (NUROB_MAIN05) ← 其他处理");
            Console.WriteLine("│   ├── 患者健康教育");
            Console.WriteLine("│   ├── 护理巡视");
            Console.WriteLine("│   ├── 交接班-患者");
            Console.WriteLine("│   ├── 交接班-物品");
            Console.WriteLine("│   ├── 交接班-毒麻药品");
            Console.WriteLine("│   ├── 交班报告原因维护");
            Console.WriteLine("│   ├── 班次维护");
            Console.WriteLine("│   ├── 毒麻药品维护");
            Console.WriteLine("│   ├── 物品字典维护");
            Console.WriteLine("│   ├── 健康教育项目维护");
            Console.WriteLine("│   ├── 巡视项目维护");
            Console.WriteLine("│   ├── 护理计划");
            Console.WriteLine("│   ├── 护理计划字典");
            Console.WriteLine("│   └── 护理记录单 ← 新增项目");
            Console.WriteLine("├── 病历字典 (NUROB_MAIN06)");
            Console.WriteLine("└── 基础字典 (NUROB_MAIN07)");
            Console.WriteLine();
            
            Console.WriteLine("右键菜单 (床头卡右键菜单):");
            Console.WriteLine("├── 患者信息查询");
            Console.WriteLine("├── 患者信息修改");
            Console.WriteLine("├── 床头卡/腕带打印");
            Console.WriteLine("├── 换床处理");
            Console.WriteLine("├── 取消入科");
            Console.WriteLine("├── 患者出院");
            Console.WriteLine("├── 患者转出");
            Console.WriteLine("├── 医嘱审核");
            Console.WriteLine("├── 护理病房");
            Console.WriteLine("├── 患者计费");
            Console.WriteLine("├── 特殊计费项目维护");
            Console.WriteLine("├── 补划价");
            Console.WriteLine("├── 费用核对");
            Console.WriteLine("├── 患者体温单");
            Console.WriteLine("├── 留观病历查询");
            Console.WriteLine("└── 护理记录单 ← 保留项目");
            Console.WriteLine();
        }
    }
}
