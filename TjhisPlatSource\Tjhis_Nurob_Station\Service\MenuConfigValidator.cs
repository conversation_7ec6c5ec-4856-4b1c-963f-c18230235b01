using System;
using System.Collections;
using System.Data;
using System.Text;

namespace Tjhis_Nurob_Station.Service
{
    /// <summary>
    /// 菜单配置验证器
    /// 用于验证菜单配置的正确性和完整性
    /// </summary>
    public class MenuConfigValidator
    {
        /// <summary>
        /// 验证护理记录单菜单配置
        /// </summary>
        /// <returns>验证结果</returns>
        public static ValidationResult ValidateNursingRecordMenuConfig()
        {
            ValidationResult result = new ValidationResult();
            
            try
            {
                MenuChangeLog.WriteLog("INFO", "菜单验证", "开始验证护理记录单菜单配置");
                
                // 创建InitDBManager实例进行测试
                InitDBManager dbManager = new InitDBManager();
                
                // 获取菜单SQL语句（使用反射调用私有方法进行测试）
                var method = typeof(InitDBManager).GetMethod("getInitMenuSql", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                
                if (method != null)
                {
                    ArrayList menuSqlList = (ArrayList)method.Invoke(dbManager, null);
                    
                    // 验证护理记录单在日常工作菜单中的配置
                    bool foundNursingRecordInDailyWork = false;
                    bool foundRightClickMenu = false;
                    
                    foreach (string sql in menuSqlList)
                    {
                        // 检查是否包含新添加的护理记录单菜单项
                        if (sql.Contains("NUROB_MAIN0570") && sql.Contains("护理记录单") && sql.Contains("NUROB_MAIN05"))
                        {
                            foundNursingRecordInDailyWork = true;
                            result.Details.Add("✓ 找到护理记录单在日常工作菜单中的配置");
                            MenuChangeLog.WriteLog("DEBUG", "菜单验证", "验证通过：护理记录单已正确配置在日常工作菜单中");
                        }
                        
                        // 检查右键菜单中的护理记录单配置
                        if (sql.Contains("NUROB_CONTEXT170") && sql.Contains("护理记录单"))
                        {
                            foundRightClickMenu = true;
                            result.Details.Add("✓ 找到护理记录单在右键菜单中的原始配置");
                        }
                    }
                    
                    // 验证结果
                    if (foundNursingRecordInDailyWork)
                    {
                        result.IsValid = true;
                        result.Message = "护理记录单菜单配置验证通过";
                        
                        if (foundRightClickMenu)
                        {
                            result.Details.Add("⚠ 注意：右键菜单中仍保留护理记录单配置，如需移除请手动删除");
                            MenuChangeLog.WriteLog("WARN", "菜单验证", "右键菜单中仍保留护理记录单配置");
                        }
                    }
                    else
                    {
                        result.IsValid = false;
                        result.Message = "护理记录单菜单配置验证失败：未找到在日常工作菜单中的配置";
                        MenuChangeLog.WriteLog("ERROR", "菜单验证", "验证失败：未找到护理记录单在日常工作菜单中的配置");
                    }
                    
                    result.Details.Add($"总共验证了 {menuSqlList.Count} 条菜单配置SQL语句");
                }
                else
                {
                    result.IsValid = false;
                    result.Message = "无法获取菜单配置方法进行验证";
                    MenuChangeLog.WriteLog("ERROR", "菜单验证", "无法通过反射获取getInitMenuSql方法");
                }
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.Message = $"菜单配置验证过程中发生异常：{ex.Message}";
                MenuChangeLog.WriteLog("ERROR", "菜单验证", "菜单配置验证异常", ex);
            }
            
            MenuChangeLog.WriteLog("INFO", "菜单验证", $"菜单验证完成 - 结果: {(result.IsValid ? "通过" : "失败")}");
            return result;
        }
        
        /// <summary>
        /// 生成菜单配置报告
        /// </summary>
        /// <returns>配置报告</returns>
        public static string GenerateMenuConfigReport()
        {
            StringBuilder report = new StringBuilder();
            
            report.AppendLine("=== 护理记录单菜单配置变更报告 ===");
            report.AppendLine($"生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine();
            
            report.AppendLine("变更内容:");
            report.AppendLine("1. 在日常工作菜单(其他处理)中新增护理记录单功能");
            report.AppendLine("   - 菜单名称: NUROB_MAIN0570");
            report.AppendLine("   - 显示文本: 护理记录单");
            report.AppendLine("   - 序号: 570");
            report.AppendLine("   - 父菜单: NUROB_MAIN05 (其他处理)");
            report.AppendLine("   - 打开窗体: TJHIS.NUROB.Severe.frmSevereRecord");
            report.AppendLine();
            
            report.AppendLine("原始配置保留:");
            report.AppendLine("1. 右键菜单中的护理记录单配置仍然保留");
            report.AppendLine("   - 菜单名称: NUROB_CONTEXT170");
            report.AppendLine("   - 显示文本: 护理记录单");
            report.AppendLine("   - 序号: 170");
            report.AppendLine("   - 菜单组: 床头卡右键菜单");
            report.AppendLine();
            
            report.AppendLine("使用说明:");
            report.AppendLine("1. 用户现在可以通过顶部菜单栏的'日常工作'菜单访问护理记录单功能");
            report.AppendLine("2. 原有的右键菜单访问方式仍然可用");
            report.AppendLine("3. 如需完全移除右键菜单中的护理记录单，请手动删除相关配置");
            report.AppendLine();
            
            // 执行验证
            ValidationResult validation = ValidateNursingRecordMenuConfig();
            report.AppendLine("配置验证结果:");
            report.AppendLine($"验证状态: {(validation.IsValid ? "通过" : "失败")}");
            report.AppendLine($"验证消息: {validation.Message}");
            
            if (validation.Details.Count > 0)
            {
                report.AppendLine("验证详情:");
                foreach (string detail in validation.Details)
                {
                    report.AppendLine($"  {detail}");
                }
            }
            
            report.AppendLine();
            report.AppendLine("=== 报告结束 ===");
            
            return report.ToString();
        }
    }
    
    /// <summary>
    /// 验证结果类
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; } = false;
        public string Message { get; set; } = string.Empty;
        public ArrayList Details { get; set; } = new ArrayList();
    }
}
