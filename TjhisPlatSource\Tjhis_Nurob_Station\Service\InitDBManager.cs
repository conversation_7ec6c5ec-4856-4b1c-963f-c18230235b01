﻿//-----------------------------------------------------------------------
//  系统名称        : 护理系统
//  子系统名称      : 
//  功能概要        : 初始化数据表
//  作  者          : 付军
//  创建时间        : 2016-08-17
//  版本            : 1.0.0.0
//-----------------------------------------------------------------------
using System;
using System.Data;
using System.Collections;
using SQL = PlatCommon.Base02.Cs02StringHelper;
using PlatCommon.Common;

namespace Tjhis.Nurob.Station.Service
{
    /// <summary>
    /// 初始化数据表
    /// </summary>
    public class InitDBManager
    {
        private NM_Service.NMService.ServerPublicClient ws = new NM_Service.NMService.ServerPublicClient();


        /// <summary>
        /// 初始化菜单表
        /// </summary>
        /// <param name="blnPlatform">是否注册到平台</param>
        public void InitMenuTable(bool blnPlatform)
        {
#if NRDOC
            ArrayList arrSql = getInitMenuSql_NRDoc();
#else
            ArrayList arrSql = getInitMenuSql();
#endif
            // 查询现有记录
            string sql = "SELECT MENU_NAME FROM COMM.SEC_MENUS_DICT T WHERE T.APPLICATION_CODE = 'NUROB'";
            DataSet ds = ws.GetDataBySql(sql, "SEC_MENUS_DICT", false);

            string beginStr = "values ('NUROB', '";
            string stopStr = "',";
            for (int i = arrSql.Count - 1; i >= 0; i--)
            {
                // 获取主键名
                sql = arrSql[i].ToString();

                int pos0 = sql.IndexOf(beginStr);
                if (pos0 < 1) continue;

                pos0 += beginStr.Length;

                int pos1 = sql.IndexOf(stopStr, pos0);
                if (pos1 < 1) continue;

                string menuName = sql.Substring(pos0, pos1 - pos0);

                // 如果存在, 不插入
                if (ds.Tables[0].Select("MENU_NAME = " + SQL.SqlConvert(menuName)).Length > 0)
                {
                    arrSql.RemoveAt(i);
                }
            }

            ws.SaveTablesData(arrSql);

            // 把菜单注册到平台
            if (blnPlatform) RegMenuInPlatform();
        }


        /// <summary>
        /// 初始化参数说明表
        /// </summary>
        public void InitAppConfigerBaseinfoTable()
        {
#if NRDOC
            return;
#endif
            ArrayList arrSql = getInitAppConfigerBaseinfoSql();
            // 查询现有记录
            string sql = "SELECT parameter_name FROM comm.app_configer_baseinfo t where t.app_name = 'NUROB'";
            DataSet ds = ws.GetDataBySql(sql, "app_configer_baseinfo", false);

            for (int i = arrSql.Count - 1; i >= 0; i--)
            {
                // 获取主键名
                sql = arrSql[i].ToString();
                string menuName = "";
                if (!string.IsNullOrEmpty(sql))
                {
                    int pos = sql.IndexOf("values");
                    string str = "values";
                    string values = sql.Substring(pos + str.Length);
                    string[] strs = values.Split(',');
                    if (strs.Length > 0)
                    {
                        menuName = strs[2].ToString();
                        if (!string.IsNullOrEmpty(menuName))
                        {
                            // 如果存在, 不插入
                            if (ds.Tables[0].Select("parameter_name = " + menuName).Length > 0)
                            {
                                arrSql.RemoveAt(i);
                            }
                        }
                    }
                }

            }
            ws.SaveTablesData(arrSql);
        }


        /// <summary>
        /// 初始化参数配置表
        /// </summary>
        public void InitAppConfigerParameterTable(string deptCode)
        {
#if NRDOC
            return;
#endif
            ArrayList arrSql = getInitAppConfigerParameterSql(deptCode);

            // 查询现有记录
            string sql = "SELECT parameter_name FROM comm.app_configer_parameter t where t.app_name = 'NUROB' and (t.DEPT_CODE  = '" + deptCode + "' OR t.DEPT_CODE = '*')";
            DataSet ds = ws.GetDataBySql(sql, "app_configer_parameter", false);
            for (int i = arrSql.Count - 1; i >= 0; i--)
            {
                // 获取主键名
                sql = arrSql[i].ToString();
                string menuName = "";
                if (!string.IsNullOrEmpty(sql))
                {
                    int pos = sql.IndexOf("values");
                    string str = "values";
                    string values = sql.Substring(pos + str.Length);
                    string[] strs = values.Split(',');
                    if (strs.Length > 0)
                    {
                        menuName = strs[3].ToString();
                        if (!string.IsNullOrEmpty(menuName))
                        {
                            // 如果存在, 不插入
                            if (ds.Tables[0].Select("parameter_name = " + menuName).Length > 0)
                            {
                                arrSql.RemoveAt(i);
                            }
                        }
                    }
                
                }

            }

            ws.SaveTablesData(arrSql);
        }



        /// <summary>
        /// 获取初始化菜单sql语句
        /// </summary>
        /// <returns>初始化菜单sql语句列表</returns>
        private ArrayList getInitMenuSql()
        {
            ArrayList arrSql = new ArrayList();

            // 记录菜单初始化开始
            MenuChangeLog.WriteLog("INFO", "菜单初始化", "开始初始化留观护理站菜单配置");

            #region 0右键菜单,共17个
            //1
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_CONTEXT100', 'MainWindow', 'GridControl', 100, '患者信息查询', '', 'parent', 'Tjhis_Nurob_Station.frmSearchInPatientInfo', 1, '1', '', 'Images/Menu/Big/00-患者信息查询.png', 'Images/Menu/Small/00-患者信息查询.png', '床头卡右键菜单', '', 'TJHIS_NUROB_PatientInfo.dll')");
            //2
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_CONTEXT105', 'MainWindow', 'GridControl', 105, '床头卡/腕带打印', '', 'parent', 'Tjhis_Nurob_Station.frmBedsideCardWristbandPrint', 1, '1', '', 'Images/Menu/Big/00-床头卡腕带打印.png', 'Images/Menu/Small/00-床头卡腕带打印.png', '床头卡右键菜单', '', 'TJHIS_NUROB_ADT.dll')");
            //3
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_CONTEXT110', 'MainWindow', 'GridControl', 110, '-', '', 'parent', '', null, '1', '', '', '', '床头卡右键菜单', '', '')");
            //4
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_CONTEXT115', 'MainWindow', 'GridControl', 115, '患者信息修改', '', 'parent', 'Tjhis_Nurob_Station.frmPatientInfoModify', 1, '1', '', 'Images/Menu/Big/00-患者信息修改.png', 'Images/Menu/Small/00-患者信息修改.png', '床头卡右键菜单', '', 'TJHIS_NUROB_ADT.dll')");
            //5
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_CONTEXT120', 'MainWindow', 'GridControl', 120, '换床处理', '', 'parent', 'Tjhis_Nurob_Station.frmBedExchange', 1, '1', '', 'Images/Menu/Big/00-换床处理.png', 'Images/Menu/Small/00-换床处理.png', '床头卡右键菜单', '', 'TJHIS_NUROB_ADT.dll')");
            //6
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_CONTEXT125', 'MainWindow', 'GridControl', 125, '取消入观', '', 'parent', 'Tjhis_Nurob_Station.frmCancelPatientIn', 1, '1', '', 'Images/Menu/Big/00-取消入科.png', 'Images/Menu/Small/00-取消入科.png', '床头卡右键菜单', '', 'TJHIS_NUROB_ADT.dll')");
            //7
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_CONTEXT130', 'MainWindow', 'GridControl', 130, '患者出观', '', 'parent', 'Tjhis_Nurob_Station.frmDisChargeProc', 1, '1', '', 'Images/Menu/Big/00-患者出院.png', 'Images/Menu/Small/00-患者出院.png', '床头卡右键菜单', '', 'TJHIS_NUROB_ADT.dll')");
            //8
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_CONTEXT135', 'MainWindow', 'GridControl', 135, '患者转出', '', 'parent', 'Tjhis_Nurob_Station.frmTransferProc', 1, '1', '', 'Images/Menu/Big/00-患者转出.png', 'Images/Menu/Small/00-患者转出.png', '床头卡右键菜单', '', 'TJHIS_NUROB_ADT.dll')");
            //9
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_CONTEXT140', 'MainWindow', 'GridControl', 140, '-', '', 'parent', '', null, '1', '', '', '', '床头卡右键菜单', '', '')");
            //10
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_CONTEXT145', 'MainWindow', 'GridControl', 145, '患者输液', '', 'parent', 'Tjhis_Nurob_Station.frmObTransfusion', 0, '1', '', 'Images/Menu/Big/07-输液.png', 'Images/Menu/Small/07-输液.png', '床头卡右键菜单', '', 'TJHIS_NUROB_Transfusion.dll')");
            //11
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_CONTEXT150', 'MainWindow', 'GridControl', 150, '护理病历', '', 'parent', 'Tjhis_Nurob_Station.FormPatientEmr', 1, '1', '', 'Images/Menu/Big/00-护理病历.png', 'Images/Menu/Small/00-护理病历.png', '床头卡右键菜单', '', 'TJHIS_NUROB_NurRec.dll')");
            //12
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_CONTEXT155', 'MainWindow', 'GridControl', 155, '患者体温', '', 'parent', 'TJHIS.NUROB.Temperature.frmNrDictTempNew', 1, '1', '', 'Images/Menu/Big/00-患者体温.png', 'Images/Menu/Small/00-患者体温.png', '床头卡右键菜单', '', 'TJHIS_NUROB_Temperature.dll')");
            //13
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_CONTEXT160', 'MainWindow', 'GridControl', 160, '医嘱执行', '', 'parent', 'Tjhis_Nurob_Station.frmOBOrdersExecute', 0, '1', '', 'Images/Menu/Big/02-医嘱执行.png', 'Images/Menu/Small/02-医嘱执行.png', '床头卡右键菜单', '', 'TJHIS_NUROB_Orders.dll')");
            //14
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_CONTEXT165', 'MainWindow', 'GridControl', 165, '留观病历查询', '', 'parent', 'Tjhis_Nurob_Station.FrmPatientEMRCenter', 1, '1', '', 'Images/Menu/Big/00-患者信息查询.png', 'Images/Menu/Small/00-患者信息查询.png', '床头卡右键菜单', '', 'TJHIS_NUROB_NurRec.dll')");
            //15
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_CONTEXT170', 'MainWindow', 'GridControl', 170, '护理记录单', '', 'parent', 'TJHIS.NUROB.Severe.frmSevereRecord', 1, '1', '', 'Images/Menu/Big/00-护理病历.png', 'Images/Menu/Small/00-护理病历.png', '床头卡右键菜单', '', 'TJHIS_NUROB_Severe.dll')");
            #endregion

            #region 1入出转菜单,共13个
            //1
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN01', 'MainWindow', '', 100, '患者入出转', '', 'parent', '', null, '1', '2', '', '', '留观护理主菜单', '', '')");
            //2
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0105', 'MainWindow', '', 105, '床头卡', '', 'NUROB_MAIN01', 'Tjhis_Nurob_Station.frmBedSideCard', null, '1', '1', 'Images/Menu/Big/01-床头卡.png', 'Images/Menu/Small/01-床头卡.png', '留观护理主菜单', '', 'TJHIS_NUROB_ADT.dll')");
            //3
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0110', 'MainWindow', '', 110, '患者入观', '', 'NUROB_MAIN01', 'Tjhis_Nurob_Station.frmPatientIn', null, '1', '1', 'Images/Menu/Big/01-患者入科.png', 'Images/Menu/Small/01-患者入科.png', '留观护理主菜单', '', 'TJHIS_NUROB_ADT.dll')");
            //4
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0115', 'MainWindow', '', 115, '取消出观', '', 'NUROB_MAIN01', 'Tjhis_Nurob_Station.frmCancelDischarge', null, '1', '1', 'Images/Menu/Big/01-取消出院.png', 'Images/Menu/Small/01-取消出院.png', '留观护理主菜单', '', 'TJHIS_NUROB_ADT.dll')");
            //5
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0120', 'MainWindow', '', 120, '取消转出', '', 'NUROB_MAIN01', 'Tjhis_Nurob_Station.frmCancelTransfer', null, '1', '1', 'Images/Menu/Big/01-取消转出.png', 'Images/Menu/Small/01-取消转出.png', '留观护理主菜单', '', 'TJHIS_NUROB_ADT.dll')");
            //6
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0125', 'MainWindow', '', 125, '入科患者查询', '', 'NUROB_MAIN01', 'Tjhis_Nurob_Station.frmSearchAdmission', null, '1', '1', 'Images/Menu/Big/01-入科患者查询.png', 'Images/Menu/Small/01-入科患者查询.png', '留观护理主菜单', '', 'TJHIS_NUROB_Report.dll')");

            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0130', 'MainWindow', '', 130, '在科患者查询', '', 'NUROB_MAIN01', 'Tjhis_Nurob_Station.frmSearchInpatient', null, '1', '1', 'Images/Menu/Big/01-在科患者查询.png', 'Images/Menu/Small/01-在科患者查询.png', '留观护理主菜单', '', 'TJHIS_NUROB_Report.dll')");
            //7
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0135', 'MainWindow', '', 135, '出科患者查询', '', 'NUROB_MAIN01', 'Tjhis_Nurob_Station.frmSearchDischarge', null, '1', '1', 'Images/Menu/Big/01-出科患者查询.png', 'Images/Menu/Small/01-出科患者查询.png', '留观护理主菜单', '', 'TJHIS_NUROB_Report.dll')");
            //8
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0140', 'MainWindow', '', 140, '等床病人查询', '', 'NUROB_MAIN01', 'Tjhis_Nurob_Station.frmSearchWaitPatient', null, '1', '1', 'Images/Menu/Big/01-等床病人查询.png', 'Images/Menu/Small/01-等床病人查询.png', '留观护理主菜单', '', 'TJHIS_NUROB_Report.dll')");
            //9
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0145', 'MainWindow', '', 145, '病人历史信息查询', '', 'NUROB_MAIN01', 'Tjhis_Nurob_Station.frmHistoryInfoQuery', null, '1', '1', 'Images/Menu/Big/01-等床病人查询.png', 'Images/Menu/Small/01-等床病人查询.png', '留观护理主菜单', '', 'TJHIS_NUROB_ADT.dll')");
            #endregion

            #region 2医疗处理菜单,共8个
            //1
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN02', 'MainWindow', '', 200, '医疗处理', '', 'parent', '', null, '1', '2', '', '', '留观护理主菜单', '', '')");
            //2
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0205', 'MainWindow', '', 205, '输血执行', '', 'NUROB_MAIN02', 'Tjhis_Nurob_Station.frmOrdersExecute_Blood', null, '1', '1', 'Images/Menu/Big/02-医嘱执行.png', 'Images/Menu/Small/02-医嘱执行.png', '留观护理主菜单', '', 'TJHIS_NUROB_Orders.dll')");
            //3 
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0210', 'MainWindow', '', 210, '护理病历', '', 'NUROB_MAIN02', 'Tjhis_Nurob_Station.FormPatientEmr', null, '1', '1', 'Images/Menu/Big/00-护理病历.png', 'Images/Menu/Small/00-护理病历.png', '留观护理主菜单', '', 'TJHIS_NUROB_NurRec.dll')");
            //4
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0215', 'MainWindow', '', 215, '医嘱执行', '', 'NUROB_MAIN02', 'Tjhis_Nurob_Station.frmOBOrdersExecute', '2', '1', '1', 'Images/Menu/Big/02-医嘱执行.png', 'Images/Menu/Small/02-医嘱执行.png', '留观护理主菜单', '', 'TJHIS_NUROB_Orders.dll')");
            //5
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0220', 'MainWindow', '', 220, '体温录入', '', 'NUROB_MAIN02', 'TJHIS.NUROB.Temperature.frmNrDictTempMutiInput', null, '1', '1', 'Images/Menu/Big/02-体温录入.png', 'Images/Menu/Small/02-体温录入.png', '留观护理主菜单', '', 'TJHIS_NUROB_Temperature.dll')");
            //6
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0225', 'MainWindow', '', 225, '体温集中录入', '', 'NUROB_MAIN02', 'TJHIS.NUROB.Temperature.frmNrTempBatchInputNew', null, '1', '1', 'Images/Menu/Big/02-体温集中录入.png', 'Images/Menu/Small/02-体温集中录入.png', '留观护理主菜单', '', 'TJHIS_NUROB_Temperature.dll')");
            //7
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT(APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0230', 'MainWindow', '', 230, '化验单标本采集', '', 'NUROB_MAIN02', 'Tjhis_Nurob_Station.frmTestsPecimenGather', null, '1', '1', 'Images/Menu/Big/02-化验单样本采集.png', 'Images/Menu/Small/02-化验单样本采集.png', '留观护理主菜单', '', 'TJHIS_NUROB_Orders.dll')");
            //8
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0235', 'MainWindow', '', 235, '护理病历解锁', '', 'NUROB_MAIN02', 'Tjhis_Nurob_Station.frmNurRecUnLock', null, '1', '1', 'Images/Menu/Big/05-病历字典.png', 'Images/Menu/Small/05-病历字典.png', '留观护理主菜单', '', 'TJHIS_NUROB_NurRec.dll')");
            //9
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0240', 'MainWindow', '', 240, '输液管理', '', 'NUROB_MAIN02', 'Tjhis_Nurob_Station.frmObTransfusionNew', null, '1', '1', 'Images/Menu/Big/07-输液.png', 'Images/Menu/Small/07-输液.png', '留观护理主菜单', '', 'TJHIS_NUROB_Transfusion.dll')");

            #endregion

            #region 3医疗查询,共13个
            //1
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN03', 'MainWindow', '', 300, '医疗查询', '', 'parent', '', null, '1', '2', '', '', '留观护理主菜单', '', '')");
            //2
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0310', 'MainWindow', '', 310, '护理病历查询', '', 'NUROB_MAIN03', 'Tjhis_Nurob_Station.frmSearchRecord', null, '1', '1', 'Images/Menu/Big/05-病历字典.png', 'Images/Menu/Small/05-病历字典.png', '留观护理主菜单', '', 'TJHIS_NUROB_NurRec.dll')");
            //3
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0315', 'MainWindow', '', 315, '检查查询', '', 'NUROB_MAIN03', 'Tjhis_Nurob_Station.frmSearchExam', null, '1', '1', 'Images/Menu/Big/02-未做检查查询.png', 'Images/Menu/Small/02-未做检查查询.png', '留观护理主菜单', '', 'TJHIS_NUROB_Report.dll')");
            //4
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0320', 'MainWindow', '', 320, '检验查询', '', 'NUROB_MAIN03', 'Tjhis_Nurob_Station.frmSearchLab', null, '1', '1', 'Images/Menu/Big/02-未做检验查询.png', 'Images/Menu/Small/02-未做检验查询.png', '留观护理主菜单', '', 'TJHIS_NUROB_Report.dll')");
            //5
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0325', 'MainWindow', '', 325, '未取处方查询', '', 'NUROB_MAIN03', 'Tjhis_Nurob_Station.frmSearchNotPresc', null, '1', '1', 'Images/Menu/Big/02-未取处方查询.png', 'Images/Menu/Small/02-未取处方查询.png', '留观护理主菜单', '', 'TJHIS_NUROB_Report.dll')");
            //6
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0330', 'MainWindow', '', 330, '未做检查查询', '', 'NUROB_MAIN03', 'Tjhis_Nurob_Station.frmSearchNotExam', null, '1', '1', 'Images/Menu/Big/02-未取处方查询.png', 'Images/Menu/Small/02-未取处方查询.png', '留观护理主菜单', '', 'TJHIS_NUROB_Report.dll')");
            //7
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0335', 'MainWindow', '', 335, '未做检验查询', '', 'NUROB_MAIN03', 'Tjhis_Nurob_Station.frmSearchNotLab', null, '1', '1', 'Images/Menu/Big/02-未做检查查询.png', 'Images/Menu/Small/02-未做检查查询.png', '留观护理主菜单', '', 'TJHIS_NUROB_Report.dll')");
            //8
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT(APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0340', 'MainWindow', '', 340, '未做手术查询', '', 'NUROB_MAIN03', 'Tjhis_Nurob_Station.frmSearchNotOperation', null, '1', '1', 'Images/Menu/Big/02-未做手术查询.png', 'Images/Menu/Small/02-未做手术查询.png', '留观护理主菜单', '', 'TJHIS_NUROB_Report.dll')");
            //9
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT(APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0350', 'MainWindow', '', 350, '输血闭环管理查询', '', 'NUROB_MAIN03', 'Tjhis_Nurob_Station.frmPatBloodTransQuery', null, '1', '1', 'Images/Menu/Big/02-医嘱执行.png', 'Images/Menu/Small/02-医嘱执行.png', '留观护理主菜单', '', 'TJHIS_NUROB_NurRec.dll')");
            //10
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT(APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0355', 'MainWindow', '', 355, '检验标本闭环管理查询', '', 'NUROB_MAIN03', 'Tjhis_Nurob_Station.frmTestSample', null, '1', '1', 'Images/Menu/Big/02-医嘱执行.png', 'Images/Menu/Small/02-医嘱执行.png', '留观护理主菜单', '', 'TJHIS_NUROB_NurRec.dll')");
            //11
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT(APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0360', 'MainWindow', '', 360, '危机值查询', '', 'NUROB_MAIN03', 'Tjhis_Nurob_Station.frmSearchCriticalValues', null, '1', '1', 'Images/Menu/Big/02-医嘱执行情况查询.png', 'Images/Menu/Small/02-医嘱执行情况查询.png', '留观护理主菜单', '', 'TJHIS_NUROB_Report.dll')");

            #endregion

            #region 4费用处理菜单,共10个
            //1
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN04', 'MainWindow', '', 400, '费用处理', '', 'parent', '', null, '1', '2', '', '', '留观护理主菜单', '', '')");
            //2
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0405', 'MainWindow', '', 405, '催补预交金', '', 'NUROB_MAIN04', 'Tjhis_Nurob_Station.frmGetPrepayments', null, '1', '1', 'Images/Menu/Big/03-费用核对.png', 'Images/Menu/Small/03-费用核对.png', '留观护理主菜单', '', 'TJHIS_NUROB_Bill.dll')");
            //3
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0410', 'MainWindow', '', 410, '预交金查询', '', 'NUROB_MAIN04', 'Tjhis_Nurob_Station.frmSearchPrepayment', null, '1', '1', 'Images/Menu/Big/03-预交金查询.png', 'Images/Menu/Small/03-预交金查询.png', '留观护理主菜单', '', 'TJHIS_NUROB_Report.dll')");
            //4
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0415', 'MainWindow', '', 415, '患者费用总清单', '', 'NUROB_MAIN04', 'Tjhis_Nurob_Station.frmCostInquiry', null, '1', '1', 'Images/Menu/Big/03-多病人多项费用录入.png', 'Images/Menu/Small/03-多病人多项费用录入.png', '留观护理主菜单', '', 'TJHIS_NUROB_Report.dll')");
            //5
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0420', 'MainWindow', '', 420, '一日费用清单', '', 'NUROB_MAIN04', 'Tjhis_Nurob_Station.frmSearchDayBill', null, '1', '1', 'Images/Menu/Big/03-一日清单打印.png', 'Images/Menu/Small/03-一日清单打印.png', '留观护理主菜单', '', 'TJHIS_NUROB_Report.dll)");
            //6
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0425', 'MainWindow', '', 425, '病区费用查询', '', 'NUROB_MAIN04', 'Tjhis_Nurob_Station.frmSearchWardBill', null, '1', '1', 'Images/Menu/Big/03-病区费用查询.png', 'Images/Menu/Small/03-病区费用查询.png', '留观护理主菜单', '', 'TJHIS_NUROB_Report.dll')");

            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0430', 'MainWindow', '', 430, '未缴费查询', '', 'NUROB_MAIN04', 'Tjhis_Nurob_Station.frmUnpaidQuery', null, '1', '1', 'Images/Menu/Big/03-一日清单打印.png', 'Images/Menu/Small/03-一日清单打印.png', '留观护理主菜单', '', 'TJHIS_NUROB_Report.dll')");

            #endregion

            #region 5其他处理菜单,共10个
            //1
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN05', 'MainWindow', '', 500, '其他处理', '', 'parent', '', null, '1', '2', '', '', '留观护理主菜单', '','')");
            //2
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0505', 'MainWindow', '', 505, '患者健康教育', '', 'NUROB_MAIN05', 'Tjhis_Nurob_Station.frmHealthEdu', null, '1', '1', 'Images/Menu/Big/07-健康教育.png', 'Images/Menu/Small/07-健康教育.png', '留观护理主菜单', '', 'TJHIS_NUROB_NurRec.dll')");
            //3
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0510', 'MainWindow', '', 510, '护理巡视', '', 'NUROB_MAIN05', 'Tjhis_Nurob_Station.frmNursingRound', null, '1', '1', 'Images/Menu/Big/07-护理巡视.png', 'Images/Menu/Small/07-护理巡视.png', '留观护理主菜单', '', 'TJHIS_NUROB_NurRec.dll')");
            //4
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0515', 'MainWindow', '', 515, '交接班-患者', '', 'NUROB_MAIN05', 'Tjhis_Nurob_Station.frmShiftWork', null, '1', '1', 'Images/Menu/Big/07-交接班-患者.png', 'Images/Menu/Small/07-交接班-患者.png', '留观护理主菜单', '', 'TJHIS_NUROB_NurRec.dll')");
            //5
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0520', 'MainWindow', '', 520, '交接班-物品', '', 'NUROB_MAIN05', 'Tjhis_Nurob_Station.frmItemsRecorded', null, '1', '1', 'Images/Menu/Big/07-交接班-物品.png', 'Images/Menu/Small/07-交接班-物品.png', '留观护理主菜单', '', 'TJHIS_NUROB_NurRec.dll')");
            //6
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0525', 'MainWindow', '', 525, '交接班-毒麻药品', '', 'NUROB_MAIN05', 'Tjhis_Nurob_Station.frmDrugsPoisonHemp', null, '1', '1', 'Images/Menu/Big/07-交接班-毒麻药品.png', 'Images/Menu/Small/07-交接班-毒麻药品.png', '留观护理主菜单', '', 'TJHIS_NUROB_NurRec.dll')");
            //7
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0530', 'MainWindow', '', 530, '交班报告原因维护', '', 'NUROB_MAIN05', 'Tjhis_Nurob_Station.frmNrDictTransReason', null, '1', '1', 'Images/Menu/Big/07-交班报告原因维护.png', 'Images/Menu/Small/07-交班报告原因维护.png', '留观护理主菜单', '', 'TJHIS_NUROB_NurRec.dll')");
            //8
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0535', 'MainWindow', '', 535, '班次维护', '', 'NUROB_MAIN05', 'Tjhis_Nurob_Station.frmSuccessionIsSet', null, '1', '1', 'Images/Menu/Big/07-班次维护.png', 'Images/Menu/Small/07-班次维护.png', '留观护理主菜单', '', 'TJHIS_NUROB_NurRec.dll')");
            //9
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0540', 'MainWindow', '', 540, '毒麻药品维护', '', 'NUROB_MAIN05', 'Tjhis_Nurob_Station.frmNrDictArticle', null, '1', '1', 'Images/Menu/Big/07-毒麻药品维护.png', 'Images/Menu/Small/07-毒麻药品维护.png', '留观护理主菜单', '', 'TJHIS_NUROB_SystemSetting.dll')");
            //10
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0545', 'MainWindow', '', 545, '物品字典维护', '', 'NUROB_MAIN05', 'Tjhis_Nurob_Station.frmGoodsMaintain', null, '1', '1', 'Images/Menu/Big/07-物品字典维护.png', 'Images/Menu/Small/07-物品字典维护.png', '留观护理主菜单', '', 'TJHIS_NUROB_NurRec.dll')");
            //11
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT(APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0550', 'MainWindow', '', 550, '健康教育项目维护', '', 'NUROB_MAIN05', 'Tjhis_Nurob_Station.frmNrDictEduBase', null, '1', '1', 'Images/Menu/Big/05-健康教育项目维护.png', 'Images/Menu/Small/05-健康教育项目维护.png', '留观护理主菜单', '', 'TJHIS_NUROB_NurRecSetting.dll')");
            //12
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT(APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values('NUROB', 'NUROB_MAIN0555', 'MainWindow', '', 555, '巡视项目维护', '', 'NUROB_MAIN05', 'Tjhis_Nurob_Station.frmNrDictInspection', null, '1', '1', 'Images/Menu/Big/05-巡视项目维护.png', 'Images/Menu/Small/05-巡视项目维护.png', '留观护理主菜单', '', 'TJHIS_NUROB_NurRecSetting.dll')");
            //13
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT(APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values('NUROB', 'NUROB_MAIN0560', 'MainWindow', '', 560, '护理计划', '', 'NUROB_MAIN05', 'Tjhis_Nurob_Station.frmNursingPlan', null, '1', '1', 'Images/Menu/Big/07-护理巡视.png', 'Images/Menu/Small/07-护理巡视.png', '留观护理主菜单', '', 'TJHIS_NUROB_NurRec.dll')");
            //14
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT(APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values('NUROB', 'NUROB_MAIN0565', 'MainWindow', '', 565, '护理计划字典', '', 'NUROB_MAIN05', 'Tjhis_Nurob_Station.frmNrDictPlanBase', null, '1', '1', 'Images/Menu/Big/05-体检单项目.png', 'Images/Menu/Small/05-体检单项目.png', '留观护理主菜单', '', 'TJHIS_NUROB_NurRecSetting.dll')");
            //15 - 新增：将护理记录单添加到日常工作菜单中
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT(APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values('NUROB', 'NUROB_MAIN0570', 'MainWindow', '', 570, '护理记录单', '', 'NUROB_MAIN05', 'TJHIS.NUROB.Severe.frmSevereRecord', null, '1', '1', 'Images/Menu/Big/00-护理病历.png', 'Images/Menu/Small/00-护理病历.png', '留观护理主菜单', '', 'TJHIS_NUROB_Severe.dll')");

            // 记录护理记录单菜单移动操作
            MenuChangeLog.LogNursingRecordMenuMove();

            #endregion

            #region 6病历字典菜单,共10个
            //1
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN06', 'MainWindow', '', 600, '病历字典', '', 'parent', '', null, '1', '2', '', '', '留观护理主菜单', '', '')");
            //2
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0605', 'MainWindow', '', 605, '数据子集', '', 'NUROB_MAIN06', 'Tjhis_Nurob_Station.frmNrDataSet', null, '1', '1', 'Images/Menu/Big/05-数据子集维护.png', 'Images/Menu/Small/05-数据子集维护.png', '留观护理主菜单', '', 'TJHIS_NUROB_NurRecSetting.dll')");
            //3
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0610', 'MainWindow', '', 610, '宏元素', '', 'NUROB_MAIN06', 'Tjhis_Nurob_Station.FormMacroSource', null, '1', '1', 'Images/Menu/Big/05-宏元素维护.png', 'Images/Menu/Small/05-宏元素维护.png', '留观护理主菜单', '', 'TJHIS_NUROB_NurRecSetting.dll')");
            //4
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0615', 'MainWindow', '', 615, '病历分类', '', 'NUROB_MAIN06', 'Tjhis_Nurob_Station.frmNrDictClass', null, '1', '1', 'Images/Menu/Big/05-病历分类.png', 'Images/Menu/Small/05-病历分类.png', '留观护理主菜单', '', 'TJHIS_NUROB_NurRecSetting.dll')");
            //5
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0620', 'MainWindow', '', 620, '病历模板维护', '', 'NUROB_MAIN06', 'Tjhis_Nurob_Station.FormEmrDoc', null, '1', '1', 'Images/Menu/Big/05-病历模板维护.png', 'Images/Menu/Small/05-病历模板维护.png', '留观护理主菜单', '', 'TJHIS_NUROB_NurRecSetting.dll')");
            //6
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0625', 'MainWindow', '', 625, '体温单项目', '', 'NUROB_MAIN06', 'TJHIS.NUROB.Temperature.frmNrDictTempBasc', null, '1', '1', 'Images/Menu/Big/05-体检单项目.png', 'Images/Menu/Small/05-体检单项目.png', '留观护理主菜单', '', 'TJHIS_NUROB_Temperature.dll')");
            //7
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0630', 'MainWindow', '', 630, '体温项目与护理单元对照', '', 'NUROB_MAIN06', 'TJHIS.NUROB.Temperature.frmNrDictTempWarditem', null, '1', '1', 'Images/Menu/Big/05-体温项目与护理单元对照.png', 'Images/Menu/Small/05-体温项目与护理单元对照.png', '留观护理主菜单', '', 'TJHIS_NUROB_Temperature.dll')");
            //8
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0635', 'MainWindow', '', 635, '体温单断开规则', '', 'NUROB_MAIN06', 'TJHIS.NUROB.Temperature.frmNrDictTempBreakRule', null, '1', '1', 'Images/Menu/Big/05-体温单断开规则.png', 'Images/Menu/Small/05-体温单断开规则.png', '留观护理主菜单', '', 'TJHIS_NUROB_Temperature.dll')");
            //9
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0640', 'MainWindow', '', 640, '护理知识库', '', 'NUROB_MAIN06', 'Tjhis_Nurob_Station.frmKnowledgeDept', null, '1', '1', 'Images/Menu/Big/05-病历字典.png', 'Images/Menu/Small/05-病历字典.png', '留观护理主菜单', '', 'TJHIS_NUROB_NurRecSetting.dll')");
            //10
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0645', 'MainWindow', '', 645, '模板属性修改', '', 'NUROB_MAIN06', 'Tjhis_Nurob_Station.frmNRDocTempletChangeTitle', null, '1', '1', 'Images/Menu/Big/06-参数维护.png', 'Images/Menu/Big/06-参数维护.png', '留观护理主菜单', '', 'TJHIS_NUROB_NurRecSetting.dll')");
            //11
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0650', 'MainWindow', '', 650, 'PDA配置', '', 'NUROB_MAIN06', 'Tjhis_Nurob_Station.FormPDAConfig', null, '1', '1', 'Images/Menu/Big/05-PDA配置.png', 'Images/Menu/Small/05-PDA配置.png', '留观护理主菜单', '', 'TJHIS_NUROB_NurRecSetting.dll')");

            #endregion

            #region 7基础字典菜单,共11个
            //1
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN07', 'MainWindow', '', 700, '基础字典', '', 'parent', '', null, '1', '2', '', '', '留观护理主菜单', '', '')");
            //2
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0705', 'MainWindow', '', 705, '床位维护', '', 'NUROB_MAIN07', 'Tjhis_Nurob_Station.frmBedRec', null, '1', '1', 'Images/Menu/Big/06-床位维护.png', 'Images/Menu/Small/06-床位维护.png', '留观护理主菜单', '', 'TJHIS_NUROB_SystemSetting.dll')");
            //3
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0710', 'MainWindow', '', 710, '费用模板维护', '', 'NUROB_MAIN07', 'Tjhis_Nurob_Station.frmBillPattern', null, '1', '1', 'Images/Menu/Big/06-费用模板维护.png', 'Images/Menu/Small/06-费用模板维护.png', '留观护理主菜单', '', 'TJHIS_NUROB_SystemSetting.dll')");
            //4
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0715', 'MainWindow', '', 715, '床位分组', '', 'NUROB_MAIN07', 'Tjhis_Nurob_Station.frmBedGroupManager', null, '1', '1', 'Images/Menu/Big/06-床位分组.png', 'Images/Menu/Small/06-床位分组.png', '留观护理主菜单', '', 'TJHIS_NUROB_SystemSetting.dll')");
            //5
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0720', 'MainWindow', '', 720, '病人关注', '', 'NUROB_MAIN07', 'Tjhis_Nurob_Station.frmPatNurseFollowManager', null, '1', '1', 'Images/Menu/Big/06-病人关注.png', 'Images/Menu/Small/06-病人关注.png', '留观护理主菜单', '', 'TJHIS_NUROB_SystemSetting.dll')");
            //6
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0725', 'MainWindow', '', 725, '护士角色设置', '', 'NUROB_MAIN07', 'Tjhis_Nurob_Station.frmNrDictNurseRole', null, '1', '1', 'Images/Menu/Big/06-护士角色设置.png', 'Images/Menu/Small/06-护士角色设置.png', '留观护理主菜单', '', 'TJHIS_NUROB_SystemSetting.dll')");
            //7
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0730', 'MainWindow', '', 730, '参数维护', '', 'NUROB_MAIN07', 'Tjhis_Nurob_Station.frmNrConfigerParameter', null, '1', '1', 'Images/Menu/Big/06-参数维护.png', 'Images/Menu/Small/06-参数维护.png', '留观护理主菜单', '', 'TJHIS_NUROB_SystemSetting.dll')");
            //8
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0735', 'MainWindow', '', 735, '角色维护', '', 'NUROB_MAIN07', 'TJHIS.Frame.frmRoleMantain', null, '1', '1', 'Images/Menu/Big/06-角色维护.png', 'Images/Menu/Small/06-角色维护.png', '留观护理主菜单', '', 'TJHIS_Frame.dll')");
            //9
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0740', 'MainWindow', '', 740, '权限维护', '', 'NUROB_MAIN07', 'TJHIS.Frame.frmUserRightMaintain', null, '1', '1', 'Images/Menu/Big/06-权限维护.png', 'Images/Menu/Small/06-权限维护.png', '留观护理主菜单', '', 'TJHIS_Frame.dll')");
            //10
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0745', 'MainWindow', '', 745, '切换护理单元', '', 'NUROB_MAIN07', 'TJHIS.Frame.SelectWardFrm', null, '1', '1', 'Images/Menu/Big/06-切换护理单元.png', 'Images/Menu/Small/06-切换护理单元.png', '留观护理主菜单', '', 'TJHIS_Frame.dll')");
            //11
            arrSql.Add(@"insert into COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0750', 'MainWindow', '', 750, '打印机配置', '', 'NUROB_MAIN07', 'TJHIS.Frame.PrinterConfigFrm', null, '1', '1', 'Images/Menu/Big/06-切换护理单元.png', 'Images/Menu/Small/06-切换护理单元.png', '留观护理主菜单', '', 'TJHIS_Frame.dll')");
            //13
            arrSql.Add(@"insert into sec_menus_dict (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0755', 'MainWindow', '', 755, '记录单设计器', '', 'NUROB_MAIN07', 'TJHIS.NUROB.Severe.frmRecordDesign', null, '1', '1', 'Images/Menu/Big/05-病历字典.png', 'Images/Menu/Small/05-病历字典.png', '留观护理主菜单', '', 'TJHIS_NUROB_Severe.dll')");
            //14
            arrSql.Add(@"insert into sec_menus_dict (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
values ('NUROB', 'NUROB_MAIN0760', 'MainWindow', '', 760, '记录单字典表', '', 'NUROB_MAIN07', 'TJHIS.NUROB.Severe.DictManagerFrm', null, '1', '1', 'Images/Menu/Big/05-病历字典.png', 'Images/Menu/Small/05-病历字典.png', '留观护理主菜单', '', 'TJHIS_NUROB_Severe.dll')");

            #endregion

            // 记录菜单初始化完成
            MenuChangeLog.WriteLog("INFO", "菜单初始化", $"菜单配置初始化完成，共生成 {arrSql.Count} 条SQL语句");

            return arrSql;
        }


        private ArrayList getInitAppConfigerBaseinfoSql()
        {
            ArrayList arrSql = new ArrayList();

            #region 费用参数
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 100, 'BACKBILLPATCH', '0', '0,1', '病人在出院，转科时是否调用后台自动划价进行补划价.0 ，不调用，1要调用。默认值是 0', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 102, 'BACKBILL_BED', '1', '0,1',         '出院划价时成人是否划床位费、空调费、杂费。0表示不划；1表示划。默认值是1要划', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 104, 'BACKBILL_BED_ZK', '1', '0,1',      '转科划价时成人划床位费、空调费、杂费。0表示不划；1表示划。默认值是1要划', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO,   PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 106, 'BACKBILL_NURSE', '1', '0,1',         '出院划价时成人是否划护理费。0表示不划；1表示划。默认值是1要划', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO,    PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 108, 'BACKBILL_NURSE_ZK', '1', '0,1',      '转科划价时成人划护理费。0表示不划；1表示划。默认值是1要划', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO,    PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 110, 'BACKBILL_TREAT', '1', '0,1',         '出院划价时成人是否划治疗医嘱。0表示不划；1表示划。默认值是1要划', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO,    PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 112, 'BACKBILL_TREAT_ZK', '1', '0,1',      '转科划价时成人划治疗医嘱。0表示不划；1表示划。默认值是1要划', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO,   PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 114, 'BACKBILL_OTHER', '1', '0,1',        '出院划价时成人是否划其他费。1表示划；0表示不划,默认值是 1', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO,    PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 116, 'BACKBILL_OTHER_ZK', '1', '0,1',      '转科划价时成人划其他费。1表示划；0表示不划,默认值是 1', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 118, 'IBILLING_ATTRIBUTE', '1', '0,1', '余额不足(可用金额,含透支)的情况仍可以继续对病人进行记费,可以指定到科室,如果值为0，不能计费1可以继续计费,默认值是1', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 120, 'NURSE_DEL_CHARGE', '0', '0,1', '护士退费是否只能退当天自己录入费用,0否,1是。', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 122, 'OVERDRAFTPREPAY_CHARGETYPES', '', '费别字典', '催款单可以跳过不催款的费别列表,如果某个费别在里面,则不会对其进行催款', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 124, 'TREAT_ROUND_MODE', '0', '0,1,2,3', '划治疗医嘱的数量取值模式。0表示不四舍五入；1表示ceiling取整;2 round取整;3 Truncate', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 126, 'PREPAYMENTS', 'yes', 'yes,no', '是否判断预交金,yes,判断，不足不允许划价，no,不判，不足仍允许病人划价', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 128, 'PREPAYMENTS_IDENTITY', '', '身份字典', '需判断预交金的身份', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 130, 'OB_PAYMENT_PLACE', '','', '设置缴费成功凭证地点', '', '', '', '')");
            #endregion 费用参数结束

            #region 费用参数,暂不用
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 200, 'BACKBILL_BED_BORN', '1', '0,1',    '出院划价时新生儿是否床位费、空调费、杂费。0表示不划；1表示划。默认值是1要划', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 202, 'BACKBILL_BED_BORN_ZK', '1', '0,1', '转科划价时新生儿是否划床位费、空调费、杂费。0表示不划；1表示划。默认值是1要划', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO,    PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 204, 'BACKBILL_NURSE_BORN', '1', '0,1',    '出院划价时新生儿是否划护理费。0表示不划；1表示划,默认值是 1', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO,    PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 206, 'BACKBILL_NURSE_BORN_ZK', '1', '0,1', '转科划价时新生儿是否划护理费。0表示不划；1表示划,默认值是 1', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO,    PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 208, 'BACKBILL_TREAT_BORN', '1', '0,1',    '出院划价时新生儿是否划治疗费。0表示不划；1表示划默认值是 1', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO,    PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 210, 'BACKBILL_TREAT_BORN_ZK', '1', '0,1', '转科划价时新生儿是否划治疗费。0表示不划；1表示划默认值是 1', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO,   PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 212, 'BACKBILL_OTHER_BORN', '1', '0,1',   '出院划价时新生儿是否划其他费。0表示不划；1表示划。默认值是 1', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO,    PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 214, 'BACKBILL_OTHER_BORN_ZK', '1', '0,1', '转科划价时新生儿是否划其他费。0表示不划；1表示划。默认值是 1', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 216, 'HOSPITAL_SANCTIFIED_AMOUNT', '','', '医院垫付金额额度', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 218, 'BACKBILL_NURSE_LIST', '','', '护士站转科划价时这些护理项目不划，出院补划价时这些护理项目算入不算出', '', '', '', '')");
            #endregion 费用参数结束     

            #region 体温单参数
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 300, 'TIME_BEGIN', '2', '', '测量体温的起始时间', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 302, 'TIME_INTERVAL', '4', '1,2,4', '体温时间间隔(1,2,4)', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 304, 'NEWBORN_TEMPRATURE', '0', '0,1', '新生儿是否启用体温单新格式，1启用，0不启用', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 306, 'STARTDAY', '1', '0,1', '计算住院天数 0 代表住院日数从住院的第二天开始算起，出院时算作住院1天1 代表住院日数从住院当天开始，出院时不算做住院,默认是1', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 308, 'TEMPERATURE_ATRIAL_FIBRILLATION_COLOR', 'Black', 'Black,Red,Blue,Green,Yellow,Purple', '体温单画房颤的阴影颜色，Black黑色, Red红色，Green绿色, Yellow黄色， Purple紫色', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 310, 'TEMPERATURE_INOUT', '0', '0,1', '体温单出入量统计，0不启用，1启用', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 312, 'TEMPERATURE_OPERATION_SHOW', '0', '0,1', '体温单手术次数的表现形式，0最新的手术在最前，且有新手术即将最早的手术顶掉，只最后显示两次，1最新的手术在最后，显示所有手术', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 314, 'TEMPERATURE_PAPER', 'Black', 'Black,Red,Blue,Green,Yellow,Purple', '体温单打印输出纸张大小', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 316, 'TEMPERATURE_REALTIME_CURVE', '0', '0,1', '体温单时间坐标是取整点时间还是精确时间，0整点，1精确时间', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 318, 'TEMPRATURE_HOUR_SYSTEM', '1', '0,1', '体温单切换12、24小时制，1为24小时， 0为12小时', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 320, 'TEMPRATURE_INTERVAL_DATA', '0', '0,1', '体温单辅助护理项目中，一天内存在多个数据时，1 取一天内的第一条 0 取最后一条', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 322, 'TEMPRATURE_OPERATION_TIMES', 'I,II,III,IV,V', '', '体温单的术后天数中，手术次数的表示形式，以逗号分开，默认为：I,II,III,IV,V', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 324, 'TEMPRATURE_POSITION_BYDAY', '6', '0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24', '体温单按日输入的项目在图形中显示位置', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 326, 'TEMPRATURE_PULSE_LIMIT', '', '', '在体温单上，脉搏高于此数值不再显示，只是在此数值刻度位置显示图形标志,空表示没有限制', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 328, 'TEMPRATURE_RIGHT', '0', '0,1', '体温单权限设置，权限高的用户可修改权限低的用户输入的数据，同级用户不能修改相互数据，0不启用，1启用', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 330, 'TEMPRATURE_SHOWTIME', '0', '0,1', '体温单显示时间方式，一天内存在多个数据时，1 1-24， 0 0-23', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 332, 'TEMPRATURE_SPACING', '1', '0,1', '单位体温之间的格数, 1为10格, 0为5格', '', '', '', '')");
            #endregion 体温单参数结束

            #region 新生儿参数,暂不用
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 400, 'NEW_BORN', '1', '0,1', '新生儿费别是否跟母亲相同，1: 和母亲不同，0: 和母亲相同', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 402, 'NEW_BORN_OUT', '1', '0,1', '母亲和新生儿是否一起出科，1为不和母亲一起，0为和母亲一起', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 404, 'NEWBORN_BED_SIGN', '_1', '_1,_2,_3', '默认的新生儿的床标尾部的附加标志， 默认值是 _1', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 406, 'NEWBORN_BORN_WHERE', '', '地区字典', '默认的新生儿的出生地', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 408, 'NEWBORN_CHARGE_TYPE', '', '费别字典', '默认的新生儿费别类型', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 410, 'NEWBORN_IDENTITY', '', '身份字典', '默认的新生儿身份类型', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 412, 'NEWBORNADMISSION_CAUSE', '新生儿', '', '默认的新生儿住院原因', '', '', '', '')");
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 414, 'PROSTRING_5', '', '所支持的床位级别BED_CLASS_DICT', '默认的新生儿床位级别', '', '', '', '')");
            #endregion 新生儿参数结束

            #region 药品参数,暂不用
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 500, 'DISPENSARY_LIST', '', '医院的药局', '医嘱摆药药局列表 --开医嘱时过滤药品 如0401;0402', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 512, 'DRUG_DISPS', '', '全院西药局', '开处方时显示西药的药局,中间以空格隔开,有专门的处方取药药局设定，可以不用在参数表中直接修改', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 514, 'CDRUG_DISPS', '', '全院中药局', '开处方时显示中药的药局,中间以空格隔开,有专门的处方取药药局设定，可以不用在参数表中直接修改', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 516, 'BEFORE_DAYS', '1','1,2,3,4,5,6', '取临时医嘱的提前天数 整数值', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 518, 'DISPENSE_START_DAY', '0', '0,-1', '0 表示摆药从当天开始 DISPENSE_START_DAY=-1 表示摆药从昨天开始. 输入整数', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 520, 'DISPENSE_START_TIME', '', '', '摆药开始时间,包含这个时间点', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 522, 'DISPENSE_STOP_TIME', '', '', '摆药截止时间,不包含这个时间点', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 524, 'DOCTOR_SPEC_FIRST', 'yes', 'yes,no', '决定是否按照指定的规格摆药,按照orders_costs中的item_spec规格进行摆药yes按照;no不按照', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 526, 'MATCH_OTHER_FIRM', 'yes', 'yes,no', '在按照医生指定规格摆药匹配不到的情况下，是否自动匹配一个相同规格不同厂家的药', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 528, 'MATCH_OTHER_SPEC', 'no', 'yes,no', '在上面两者都匹配不到的情况下，是否匹配其他不同规格的药', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 530, 'TYPE', '1', '1,0', '摆药方式 1代表预交金不足就不进行摆药;0代表按剩余金额摆药!', '', '', '', '')");
            #endregion 药品参数,暂不用

            #region 其他参数
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 600, 'ORDER_NOT_CHECK', '', '', '转科不校验医嘱代码', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 602, 'SETTLED_INDICATOR', '1', '0,1', '在护士站办理出院时是否判断病人有未结费用，0不判断,1判断 默认值是 1 ', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 604, 'CLINIC_CLASS', '0', '1,0', '核算组使用方式，1为核算组核算，0为诊疗组核算', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 606, 'CONTAINER_CARRIER_EXIST', '0', '0,1', '标本采集时，试管上是否已经存在条码（1-已存在，0-不存在)', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 608, 'CONTAINER_CARRIER_PDA', '0', '0,1', '标本采集时，是否使用PDA（1-使用，0-不使用)', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 610, 'TRANS_BLOOD_APPLY_NUM_MIN_LEN', '4', '', '输血申请单号最小位数', '', '', '', '')");
            #endregion 其他参数结束

            #region 其他参数,暂不用
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 700, 'NEED_ORDERS_STATUS', '0', '0,1', '医嘱审核是否需要医嘱转抄操作（0-医生提交后就可以审核，1-护士转抄后才可以审核)', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 702, 'SUPPOR_DOCTOR', '1', '0,1', '是否支持医生站停用医嘱开处方功能; 为时0可停用医嘱,允许开处方，为1时不能停用医嘱,不能开处方 ', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 704, 'ORDER_COSTS', '1', '0,1', '当参数suppor_doctor为1时，是否医生站开医嘱时候自动生成计价项目并且可以录入材料等计价项目，1为是，0为否并且在医嘱校对时生成计价项目  ', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 706, 'BED_CARD_GEST_WEEKS', '', '', '床头卡加孕周, 0 否，1 是', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 708, 'WARD_CONTACT_TEL', '', '', '科室联系电话', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 710, 'BEDWITHDEPT', '1', '0,1', '床位属于科室还是护理单元（1-属于科室，0-属于护理单元)', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 712, 'BLOOD_SUGAR_ITEMS', '', '', '血糖类型, 分隔', '', '', '', '')");


            #endregion 其他参数,暂不用

            #region 系统级参数
            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 800, 'BILL_ON_RULEIN_ITEM_CODE_LIST', '', '计入不计出项目列表', '医嘱计费中要求按计入不计出项目进行划价的项目列表“,”分隔', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 812, 'EX_BILLING', '0', '0,1', '耗材库存不足是否允许计费,0,允许,1,不允许', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 814, 'EXP_STOCK', '0', '0,1', '是否减消耗品库存 1-减，0-不减', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 816, 'OVERDRAFT_WAY', '2', '1,2', '平台级；取值：1 - 各个透支排斥；2 - 各个透支联合，即相加；', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 818, 'PAYPERMENT_BALANCE', '0', '1,0', '1：余额中不含透支额  0 ：余额中含透支额', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 820, 'OB_CHARGE_DEPT_NURSWS', '','', '设置收费的执行科室代码,科室代码间用“;”间隔', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 822, 'OB_CHARGE_ITEM_CODE', '','', '设置收费的项目代码,项目代码间用“;”号间隔', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 823, 'OB_DEPT_PREPAY_BALANCE', '','', '设置每个留观科室所允许的预交金余额', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 824, 'OUTP_BILL_OPERATOR_NO', '1111','', '一卡通中自动收费中收款员工号', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 826, 'APPLICATION_MAX_IDLE_MINUTES', '5', '', '最大空闲时间(分钟)', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 828, 'CHECKLISRESULTALARM', '0', '0,1', '危机值提示是否，0不提示，1提示', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 830, 'PAT_EMR_CENTER_URL', 'http://*************:8089/HIIPXYProject/xds/excelXdsMenu.do?verbId=init&patId={0}&hspId=582759327', '', '病人病历中心URL', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 832, 'TEST_RESULT_URL', 'http://*************:8089/HIIPXYProject/xds/unifiedReportCenter.do?verbId=Display&patID={0}&visitID={1}&reportsubclass=LAB&applyNo={2}', '', '化验结果报告地址', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 834, 'HERP_URL', 'http://*************:8999/services/Mirth?wsdl', '', 'HERP_URL', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 836, 'HISTORY_INFO_URL', 'http://*************:8089/HIIPXYProject/xds/excelXdsMenu.do?verbId=init&patId={0}&hsp', '', '留观病历查询URL', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 838, 'PLAT_UP', '', '', '体温单_平台的URL', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 839, 'PLAT_URL', '', '', '体温单_平台的URL', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo  (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 840, 'ADMINISTRATION', '', '', '(留观输液)生成输液单时，有效处方的用药途径范围，多用药途径间用“,”间隔', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 842, 'PORT', '0', '0,1,2,3,4', '(留观输液)通讯端口（1-并口一;2-并口二;3-串口一;4-串口二)', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 844, 'BAUDSPEED', '9600', '9600,19200,38400', '(留观输液)串口速率(9600,19200,38400)', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 846, 'LIMITDAYS', '30', '1-365', '(留观输液)查询天数限制，超过则提示', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 848, 'OB_LY_URL', '', '', '乐约URL地址配置,设置地址时orderid的值用{0}代替', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 850, 'RIQI', '4', '', '高值耗材日期界限', '', '', '', '')");

            arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
                                values ('NUROB', 714, 'RECORD_LIMIT_TIME', '6', '', '转科后护理记录单补录时限,单位:小时', '', '', '', '')");

            #endregion 系统级参数结束  

            #region 删除的参数
            ////arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
            ////                    values ('NUROB', 902, 'BILLENTR', '1', '0,1', '批量计价录入时是否判断可用金额  0 不判断 1 判断 ，默认为1', '', '', '', '')");

            ////arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
            ////                    values ('NUROB', 904, 'NURSE_BILL_INPUT', '其它价表', '输入法设置字典', '护士站计价录入调用视图名', '', '', '', '')");

            ////arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
            ////                    values ('NUROB', 906, 'CLINIC_BILL_AUTO', '1', '0,1', '置换诊疗组时是否进行提示划价，1-提示，0-不提示，默认为1', '', '', '', '')");

            ////arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
            ////                    values ('NUROB', 908, 'PAYPERMENT_CHARGE', '', '费别列表选择', '不进行预交金余额验证的患者费别，维护费别患者住院期间不交预交金;不同费别之间用“；”分隔', '', '', '', '')");

            ////arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
            ////                    values ('NUROB', 910, 'ORDERCOSTS_VERIFIED_MODIFY', '0', '0,1', '医嘱被转抄校对后是否允许添加删除修改计价项目 0，不允许 ,1允许,默认值是0', '', '', '', '')");

            ////arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
            ////                    values ('NUROB', 912, 'ORDER_QIANMING', '1', '0,1', '护士站在校对医嘱、录入费用的时候是否弹出密码校验窗口,0为不弹出，1为弹出', '', '', '', '')");

            ////arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
            ////                    values ('NUROB', 914, 'ISFILTER', '0', '0,1', '是否支持续静滴这种模式 ,1支持用药途径过滤，0不支持用药途径过滤,默认是0', '', '', '', '')");

            ////arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
            ////                    values ('NUROB', 916, 'CONTINUE_ORDER', '续静滴', '所有的用药途径', '续静途径列表 ,如果支持续静滴模式，这个列表的用药途径都会进行过滤以逗号隔开,要与参数 ISFILTER协同使用,默认值是 续静滴', '', '', '', '')");

            ////arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
            ////                    values ('NUROB', 918, 'ENABLEMESSAGE', '0', '0,1', '病人信息更新时，是否刷新床头卡, 1为刷新, 0为不刷新', '', '', '', '')");

            ////arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
            ////                    values ('NUROB', 920, 'TRANS_BLOOD_ADVERSE_TEMPLETE_CODE', '', '', '输血不良反应模板编码', '', '', '', '')");

            ////arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
            ////                    values ('NUROB', 922, 'PLAT_UP', '0', '0,1', '保存病历时候时候上传平台，0不上传，1上传', '', '', '', '')");

            ////arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
            ////                    values ('NUROB', 924, 'LIS_ALARM_TREAT_ITEMS', '', '', '危急值处理说明选项', '', '', '', '')");

            ////arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
            ////                    values ('NURINP', 924, 'DEPT_RECORDTYPE', '', '', '科室默认提取出入量数据的记录单', '', '', '', '')");

            ////arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
            ////                    values ('NUROB', 926, 'TEMPRATURE_DISABLE_GENERATE_TRANS', '', '', '不自动产生转科事件的病区代码，以逗号隔开', '', '', '', '')");

            ////arrSql.Add(@"insert into comm.app_configer_baseinfo (APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE)
            ////                    values ('NUROB', 928, 'TEMPRATURE_ADDITIONAL_NEWBORN', '0', '0,1', '新生儿体温单是否需要启用添加附加项，0不启用，1启用', '', '', '', '')");

            #endregion 删除的参数结束

            return arrSql;
        }


        private ArrayList getInitAppConfigerParameterSql(string wardCode)
        {
            ArrayList arrSql = new ArrayList();

            //string insertStr = His00GlobalVars.bIsHisUnit ? "insert into comm.APP_CONFIGER_PARAMETER (APP_NAME ,DEPT_CODE ,EMP_NO ,PARAMETER_NAME ,PARAMETER_VALUE,POSITION ,MEMO, HIS_UNIT_CODE) " 
            //                                              : "insert into comm.APP_CONFIGER_PARAMETER (APP_NAME ,DEPT_CODE ,EMP_NO ,PARAMETER_NAME ,PARAMETER_VALUE,POSITION ,MEMO) ";

            #region 费用参数
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','BACKBILLPATCH', '0', '*', '*','"+ His00GlobalVars.His_Unit_Code +"') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','BACKBILLPATCH', '0', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','BACKBILL_BED', '1', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','BACKBILL_BED', '1', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','BACKBILL_BED_ZK', '1', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','BACKBILL_BED_ZK', '1', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','BACKBILL_NURSE', '1', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','BACKBILL_NURSE', '1', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','BACKBILL_NURSE_ZK', '1', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','BACKBILL_NURSE_ZK', '1', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','BACKBILL_OTHER', '1', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','BACKBILL_OTHER', '1', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','BACKBILL_OTHER_ZK', '1', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','BACKBILL_OTHER_ZK', '1', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','BACKBILL_TREAT', '1', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','BACKBILL_TREAT', '1', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','BACKBILL_TREAT_ZK', '1', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','BACKBILL_TREAT_ZK', '1', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','IBILLING_ATTRIBUTE', '1', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','IBILLING_ATTRIBUTE', '1', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','NURSE_DEL_CHARGE', '0', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','NURSE_DEL_CHARGE', '0', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','OVERDRAFTPREPAY_CHARGETYPES', '', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','OVERDRAFTPREPAY_CHARGETYPES', '', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','TREAT_ROUND_MODE', '0', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','TREAT_ROUND_MODE', '0', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','PREPAYMENTS', 'yes', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','PREPAYMENTS', 'yes', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','PREPAYMENTS_IDENTITY', '', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','PREPAYMENTS_IDENTITY', '', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','OB_PAYMENT_PLACE', '', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','OB_PAYMENT_PLACE', '', '*', '*') "));
            #endregion 费用参数

            #region 费用参数 暂不用
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','BACKBILL_BED_BORN', '1', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','BACKBILL_BED_BORN', '1', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','BACKBILL_BED_BORN_ZK', '1', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','BACKBILL_BED_BORN_ZK', '1', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','BACKBILL_NURSE_BORN', '1','*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','BACKBILL_NURSE_BORN', '1','*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','BACKBILL_NURSE_BORN_ZK', '1','*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','BACKBILL_NURSE_BORN_ZK', '1','*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','BACKBILL_OTHER_BORN', '1','*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','BACKBILL_OTHER_BORN', '1','*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','BACKBILL_OTHER_BORN_ZK', '1','*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','BACKBILL_OTHER_BORN_ZK', '1','*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','BACKBILL_TREAT_BORN', '1','*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','BACKBILL_TREAT_BORN', '1','*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','BACKBILL_TREAT_BORN_ZK', '1','*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','BACKBILL_TREAT_BORN_ZK', '1','*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*', 'HOSPITAL_SANCTIFIED_AMOUNT', '0', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*', 'HOSPITAL_SANCTIFIED_AMOUNT', '0', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*', 'BACKBILL_NURSE_LIST', '', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*', 'BACKBILL_NURSE_LIST', '', '*', '*') "));
            #endregion 费用参数 暂不用

            #region 体温单参数
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*', 'TIME_BEGIN', '2', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*', 'TIME_BEGIN', '2', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*', 'TIME_INTERVAL', '4', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*', 'TIME_INTERVAL', '4', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','NEWBORN_TEMPRATURE', '0', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','NEWBORN_TEMPRATURE', '0', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','STARTDAY', '1', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','STARTDAY', '1', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','TEMPERATURE_ATRIAL_FIBRILLATION_COLOR', 'Black', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','TEMPERATURE_ATRIAL_FIBRILLATION_COLOR', 'Black', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','TEMPERATURE_INOUT', '0', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','TEMPERATURE_INOUT', '0', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','TEMPERATURE_OPERATION_SHOW', '0', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','TEMPERATURE_OPERATION_SHOW', '0', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','TEMPERATURE_PAPER', 'A4', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','TEMPERATURE_PAPER', 'A4', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','TEMPERATURE_REALTIME_CURVE', '0', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','TEMPERATURE_REALTIME_CURVE', '0', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','TEMPRATURE_HOUR_SYSTEM', '1', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','TEMPRATURE_HOUR_SYSTEM', '1', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','TEMPRATURE_INTERVAL_DATA', '0', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','TEMPRATURE_INTERVAL_DATA', '0', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','TEMPRATURE_OPERATION_TIMES', 'I,II,III,IV,V', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','TEMPRATURE_OPERATION_TIMES', 'I,II,III,IV,V', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','TEMPRATURE_POSITION_BYDAY', '6', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','TEMPRATURE_POSITION_BYDAY', '6', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','TEMPRATURE_PULSE_LIMIT', '', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','TEMPRATURE_PULSE_LIMIT', '', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','TEMPRATURE_RIGHT', '0', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','TEMPRATURE_RIGHT', '0', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','TEMPRATURE_SPACING', '1', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','TEMPRATURE_SPACING', '1', '*', '*') "));
            #endregion 体温单参数结束

            #region 新生儿参数 暂不用
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','NEW_BORN', '1', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','NEW_BORN', '1', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','NEW_BORN_OUT', '1', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','NEW_BORN_OUT', '1', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','NEWBORN_BED_SIGN', '_1', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','NEWBORN_BED_SIGN', '_1', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','NEWBORN_BORN_WHERE', '', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','NEWBORN_BORN_WHERE', '', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','NEWBORN_CHARGE_TYPE', '', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','NEWBORN_CHARGE_TYPE', '', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','NEWBORN_IDENTITY', '', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','NEWBORN_IDENTITY', '', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','NEWBORNADMISSION_CAUSE', '新生儿', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','NEWBORNADMISSION_CAUSE', '新生儿', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','PROSTRING_5', '', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','PROSTRING_5', '', '*', '*') "));
            #endregion 新生儿参数结束

            #region 药品参数 暂不用
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','DISPENSARY_LIST', '', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','DISPENSARY_LIST', '', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','DRUG_DISPS', '', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','DRUG_DISPS', '', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','BEFORE_DAYS', '3', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','BEFORE_DAYS', '3', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','DISPENSE_START_DAY', '0', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','DISPENSE_START_DAY', '0', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','DISPENSE_START_TIME', '16:00', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','DISPENSE_START_TIME', '16:00', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','DISPENSE_STOP_TIME', '16:00', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','DISPENSE_STOP_TIME', '16:00', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','DOCTOR_SPEC_FIRST', 'YES', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','DOCTOR_SPEC_FIRST', 'YES', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','MATCH_OTHER_FIRM', 'NO', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','MATCH_OTHER_FIRM', 'NO', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','MATCH_OTHER_SPEC', 'NO', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','MATCH_OTHER_SPEC', 'NO', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','TYPE', '0', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','TYPE', '0', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','CDRUG_DISPS', '', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','CDRUG_DISPS', '', '*', '*') "));
            #endregion 药品参数结束

            #region 其它参数
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','ORDER_NOT_CHECK', '', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','ORDER_NOT_CHECK', '', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','SETTLED_INDICATOR', '1', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','SETTLED_INDICATOR', '1', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','CLINIC_CLASS', '0', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','CLINIC_CLASS', '0', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','CONTAINER_CARRIER_EXIST', '1', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','CONTAINER_CARRIER_EXIST', '1', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','CONTAINER_CARRIER_PDA', '0', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','CONTAINER_CARRIER_PDA', '0', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','TRANS_BLOOD_APPLY_NUM_MIN_LEN', '4', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','TRANS_BLOOD_APPLY_NUM_MIN_LEN', '4', '*', '*') "));
            #endregion 其它参数 结束

            #region 其它参数 暂不用
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*', 'NEED_ORDERS_STATUS', '1', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*', 'NEED_ORDERS_STATUS', '1', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','SUPPOR_DOCTOR', '1', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','SUPPOR_DOCTOR', '1', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','ORDER_COSTS', '1', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','ORDER_COSTS', '1', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','BED_CARD_GEST_WEEKS', '0', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','BED_CARD_GEST_WEEKS', '0', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*','WARD_CONTACT_TEL', '', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*','WARD_CONTACT_TEL', '', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*', 'BEDWITHDEPT', '1', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*', 'BEDWITHDEPT', '1', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '" + wardCode + "', '*', 'BLOOD_SUGAR_ITEMS', '', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '" + wardCode + "', '*', 'BLOOD_SUGAR_ITEMS', '', '*', '*') "));
            #endregion 其它参数 暂不用结束

            #region 系统参数
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '*', '*','BILL_ON_RULEIN_ITEM_CODE_LIST', '', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '*', '*','BILL_ON_RULEIN_ITEM_CODE_LIST', '', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '*', '*','EX_BILLING', '0', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '*', '*','EX_BILLING', '0', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '*', '*','EXP_STOCK', '0', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '*', '*','EXP_STOCK', '0', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '*', '*','OVERDRAFT_WAY', '2', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '*', '*','OVERDRAFT_WAY', '2', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '*', '*','PAYPERMENT_BALANCE', '0', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '*', '*','PAYPERMENT_BALANCE', '0', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '*', '*','OB_CHARGE_DEPT_NURSWS', '0230;01020103;01200204;01200203;01200121;01200122;01020504;023001;02300101;02300102;02300103;02300104;02300105;02300501;02420101;02420103;01200202', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '*', '*','OB_CHARGE_DEPT_NURSWS', '0230;01020103;01200204;01200203;01200121;01200122;01020504;023001;02300101;02300102;02300103;02300104;02300105;02300501;02420101;02420103;01200202', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '*', '*','OB_CHARGE_ITEM_CODE', '', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '*', '*','OB_CHARGE_ITEM_CODE', '', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '*', '*','OB_DEPT_PREPAY_BALANCE', '0', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '*', '*','OB_DEPT_PREPAY_BALANCE', '0', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '*', '*','OUTP_BILL_OPERATOR_NO', '1111', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '*', '*','OUTP_BILL_OPERATOR_NO', '1111', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '*', '*', 'APPLICATION_MAX_IDLE_MINUTES', '5', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '*', '*', 'APPLICATION_MAX_IDLE_MINUTES', '5', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB',  '*', '*','CHECKLISRESULTALARM', '0', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB',  '*', '*','CHECKLISRESULTALARM', '0', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '*', '*', 'PAT_EMR_CENTER_URL', 'http://*************:8089/HIIPXYProject/xds/excelXdsMenu.do?verbId=init&patId={0}&hspId=582759327', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '*', '*', 'PAT_EMR_CENTER_URL', 'http://*************:8089/HIIPXYProject/xds/excelXdsMenu.do?verbId=init&patId={0}&hspId=582759327', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '*', '*', 'TEST_RESULT_URL', 'http://*************:8089/HIIPXYProject/xds/unifiedReportCenter.do?verbId=Display&patID={0}&visitID={1}&reportsubclass=LAB&applyNo={2}', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '*', '*', 'TEST_RESULT_URL', 'http://*************:8089/HIIPXYProject/xds/unifiedReportCenter.do?verbId=Display&patID={0}&visitID={1}&reportsubclass=LAB&applyNo={2}', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '*', '*', 'HERP_URL', 'http://*************:8999/services/Mirth?wsdl', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '*', '*', 'HERP_URL', 'http://*************:8999/services/Mirth?wsdl', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '*', '*','HISTORY_INFO_URL', 'http://*************:8089/HIIPXYProject/xds/excelXdsMenu.do?verbId=init&patId={0}&hsp', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '*', '*','HISTORY_INFO_URL', 'http://*************:8089/HIIPXYProject/xds/excelXdsMenu.do?verbId=init&patId={0}&hsp', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '*', '*','PLAT_UP', '1', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '*', '*','PLAT_UP', '1', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '*', '*','PLAT_URL', 'http://*************:1500/Report.TJ/', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '*', '*','PLAT_URL', 'http://*************:1500/Report.TJ/', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '*', '*','ADMINISTRATION', '', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '*', '*','ADMINISTRATION', '', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB',  '*', '*','PORT', '0', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB',  '*', '*','PORT', '0', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB',  '*', '*','BAUDSPEED', '9600', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB',  '*', '*','BAUDSPEED', '9600', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB',  '*', '*','LIMITDAYS', '30', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB',  '*', '*','LIMITDAYS', '30', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB',  '*', '*','OB_LY_URL', 'http://leyue12320.leyue100.com/normal/paycenter/pay?content=orderid:{0},busType:3', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB',  '*', '*','OB_LY_URL', 'http://leyue12320.leyue100.com/normal/paycenter/pay?content=orderid:{0},busType:3', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB',  '*', '*','RIQI', '190501', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB',  '*', '*','RIQI', '190501', '*', '*') "));
            //arrSql.Add(insertStr + (His00GlobalVars.bIsHisUnit ? " values ('NUROB', '*', '*', 'RECORD_LIMIT_TIME', '6', '*', '*','" + His00GlobalVars.His_Unit_Code + "') "
            //                                                   : " values ('NUROB', '*', '*', 'RECORD_LIMIT_TIME', '6', '*', '*') "));
            #endregion 系统参数结束

            #region 删除的参数 暂不用
            ////arrSql.Add(@"insert into comm.APP_CONFIGER_PARAMETER (APP_NAME ,DEPT_CODE ,EMP_NO ,PARAMETER_NAME ,PARAMETER_VALUE,POSITION ,MEMO) " +
            ////            "        values ('NUROB', '" + wardCode + "', '*','CLINIC_BILL_AUTO', '1', '*', '*')");
            ////arrSql.Add(@"insert into comm.APP_CONFIGER_PARAMETER (APP_NAME ,DEPT_CODE ,EMP_NO ,PARAMETER_NAME ,PARAMETER_VALUE,POSITION ,MEMO) " +
            ////            "        values ('NUROB', '" + wardCode + "', '*','PAYPERMENT_CHARGE', '', '*', '*')");
            ////arrSql.Add(@"insert into comm.APP_CONFIGER_PARAMETER (APP_NAME ,DEPT_CODE ,EMP_NO ,PARAMETER_NAME ,PARAMETER_VALUE,POSITION ,MEMO) " +
            ////            "        values ('NUROB', '" + wardCode + "', '*','ORDERCOSTS_VERIFIED_MODIFY', '0', '*', '*')");
            ////arrSql.Add(@"insert into comm.APP_CONFIGER_PARAMETER (APP_NAME ,DEPT_CODE ,EMP_NO ,PARAMETER_NAME ,PARAMETER_VALUE,POSITION ,MEMO) " +
            ////            "        values ('NUROB', '" + wardCode + "', '*','ORDER_QIANMING', '1', '*', '*')");
            ////arrSql.Add(@"insert into comm.APP_CONFIGER_PARAMETER (APP_NAME ,DEPT_CODE ,EMP_NO ,PARAMETER_NAME ,PARAMETER_VALUE,POSITION ,MEMO) " +
            ////            "        values ('NUROB', '" + wardCode + "', '*','ISFILTER', '0', '*', '*')");
            ////arrSql.Add(@"insert into comm.APP_CONFIGER_PARAMETER (APP_NAME ,DEPT_CODE ,EMP_NO ,PARAMETER_NAME ,PARAMETER_VALUE,POSITION ,MEMO) " +
            ////            "        values ('NUROB', '" + wardCode + "', '*','CONTINUE_ORDER', '续静滴', '*', '*')");
            ////arrSql.Add(@"insert into comm.APP_CONFIGER_PARAMETER (APP_NAME ,DEPT_CODE ,EMP_NO ,PARAMETER_NAME ,PARAMETER_VALUE,POSITION ,MEMO) " +
            ////            "        values ('NUROB', '" + wardCode + "', '*','ENABLEMESSAGE', '1', '*', '*')");
            ////arrSql.Add(@"insert into comm.APP_CONFIGER_PARAMETER (APP_NAME ,DEPT_CODE ,EMP_NO ,PARAMETER_NAME ,PARAMETER_VALUE,POSITION ,MEMO) " +
            ////            "        values ('NUROB',  '*', '*','TRANS_BLOOD_ADVERSE_TEMPLETE_CODE', '', '*', '*')");
            ////arrSql.Add(@"insert into comm.APP_CONFIGER_PARAMETER (APP_NAME ,DEPT_CODE ,EMP_NO ,PARAMETER_NAME ,PARAMETER_VALUE,POSITION ,MEMO) " +
            ////            "        values ('NUROB',  '*', '*','PLAT_UP', '', '*', '*')");
            ////arrSql.Add(@"insert into comm.APP_CONFIGER_PARAMETER (APP_NAME ,DEPT_CODE ,EMP_NO ,PARAMETER_NAME ,PARAMETER_VALUE,POSITION ,MEMO) " +
            ////            "        values ('NUROB',  '*', '*','LIS_ALARM_TREAT_ITEMS', '', '*', '*')");
            ////arrSql.Add(@"insert into comm.APP_CONFIGER_PARAMETER (APP_NAME ,DEPT_CODE ,EMP_NO ,PARAMETER_NAME ,PARAMETER_VALUE,POSITION ,MEMO) " +
            ////            "        values ('NUROB', '" + wardCode + "', '*','DEPT_RECORDTYPE', '0', '*', '*')");
            ////arrSql.Add(@"insert into comm.APP_CONFIGER_PARAMETER (APP_NAME ,DEPT_CODE ,EMP_NO ,PARAMETER_NAME ,PARAMETER_VALUE,POSITION ,MEMO) " +
            ////            "        values ('NUROB', '" + wardCode + "', '*','BILLENTR', '1', '*', '*')");
            ////arrSql.Add(@"insert into comm.APP_CONFIGER_PARAMETER (APP_NAME ,DEPT_CODE ,EMP_NO ,PARAMETER_NAME ,PARAMETER_VALUE,POSITION ,MEMO) " +
            ////            "        values ('NUROB', '" + wardCode + "', '*','NURSE_BILL_INPUT', '其它价表', '*', '*')");
            ////arrSql.Add(@"insert into comm.APP_CONFIGER_PARAMETER (APP_NAME ,DEPT_CODE ,EMP_NO ,PARAMETER_NAME ,PARAMETER_VALUE,POSITION ,MEMO) " +
            ////            "        values ('NUROB',  '*', '*','TEMPRATURE_ADDITIONAL_NEWBORN', '0', '*', '*')");
            ////arrSql.Add(@"insert into comm.APP_CONFIGER_PARAMETER (APP_NAME ,DEPT_CODE ,EMP_NO ,PARAMETER_NAME ,PARAMETER_VALUE,POSITION ,MEMO) " +
            ////            "        values ('NUROB',  '*', '*','TEMPRATURE_DISABLE_GENERATE_TRANS', '', '*', '*')");
            #endregion 删除的参数结束

            return arrSql;
        }


        //获取一个用户的最大权限 
        public String getMaxRight(String user_name)
        {
            String sql = "select * from NROB_DICT_NURSE_ROLE where NURSE_CODE=" + SQL.SqlConvert(user_name) + " ORDER BY NURSE_ROLE ASC ";
            DataTable dt = ws.GetDataBySql(sql, "NROB_DICT_NURSE_ROLE", false).Tables[0];
            if (dt.Rows.Count > 0)
            {
                return dt.Rows[0]["NURSE_ROLE"].ToString();
            }
            else
            {
                return "99";
            }
        }


        /// <summary>
        /// 在平台中注册应用程序菜单
        /// </summary>
        /// <returns></returns>
        public bool RegMenuInPlatform()
        {
            string sql = "SELECT * FROM SEC_MENUS_DICT WHERE APPLICATION_CODE = 'NUROB'";
            DataSet dsMenu = this.ws.GetDataBySql(sql, "SEC_MENUS_DICT", true);

            sql = "SELECT * FROM SECURITY_TEMPLATE WHERE APPLICATION = 'NUROB'";
            DataSet dsMenuP = this.ws.GetDataBySql(sql, "SECURITY_TEMPLATE", true);
            dsMenuP.Tables[0].PrimaryKey = new DataColumn[] { dsMenuP.Tables[0].Columns["APPLICATION"],
                                                              dsMenuP.Tables[0].Columns["WINDOW"],
                                                              dsMenuP.Tables[0].Columns["CONTROL"] };

            // 进行比较 插入/修改
            DataRow[] drFind = null;
            foreach (DataRow drMenu in dsMenu.Tables[0].Rows)
            {
                string filter = "APPLICATION = 'NUROB' AND WINDOW = {0} AND CONTROL = {1}";

                // ------
                string window = drMenu["MENU_NAME_FULL"].ToString();
                int pos = window.IndexOf('.', 0);
                if (pos >= 0) pos = window.IndexOf('.', pos + 1);

                if (pos >= 0)
                {
                    window = window.Substring(0, pos);
                }
                else
                {
                    window = (drMenu["FORM_CONTROL"].ToString().Length == 0 ? "MainWindow" : drMenu["FORM_CONTROL"].ToString());
                }

                // ------
                filter = string.Format(filter, SQL.SqlConvert(window), SQL.SqlConvert(drMenu["MENU_NAME_FULL"].ToString()));

                drFind = dsMenuP.Tables[0].Select(filter);

                DataRow drEdit = null;
                if (drFind.Length == 0)
                {
                    drEdit = dsMenuP.Tables[0].NewRow();

                    drEdit["APPLICATION"] = drMenu["APPLICATION_CODE"];
                    drEdit["WINDOW"] = window;
                    drEdit["CONTROL"] = drMenu["MENU_NAME_FULL"];
                }
                else
                {
                    drEdit = drFind[0];
                }

                // 进行赋值
                drEdit["DESCRIPTION"] = drMenu["MENU_TEXT"];
                drEdit["OBJECT_TYPE"] = "menuitem";
                drEdit["CONTROL_PARENT"] = drMenu["SUPPER_MENU_FULL"];
                drEdit["ITEM_NO"] = drMenu["SERIAL_NO"];
                drEdit["MEMO"] = drMenu["MENU_GROUP"];

                if (drFind.Length == 0)
                {
                    dsMenuP.Tables[0].Rows.Add(drEdit);
                }
            }

            // 进行删除
            drFind = dsMenuP.Tables[0].Select();
            for (int i = drFind.Length - 1; i >= 0; i--)
            {
                if (drFind[i].RowState == DataRowState.Unchanged) drFind[i].Delete();
            }

            // 进行保存
            dsMenuP.Tables[0].PrimaryKey = new DataColumn[] { dsMenuP.Tables[0].Columns["APPLICATION"],
                                                              dsMenuP.Tables[0].Columns["WINDOW"],
                                                              dsMenuP.Tables[0].Columns["CONTROL"],
                                                              dsMenuP.Tables[0].Columns["ITEM_NO"] };
            this.ws.SaveDataSet(dsMenuP);

            return true;
        }
    }
}